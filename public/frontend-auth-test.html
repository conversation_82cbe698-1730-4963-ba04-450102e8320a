<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端权限测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #6c757d;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔐 前端权限测试</h1>
    <p>测试前端认证状态和权限检查</p>

    <div class="test-section">
        <h2>📋 当前状态检查</h2>
        <button class="test-button" onclick="checkCurrentState()">检查当前状态</button>
        <div id="state-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔑 管理员登录测试</h2>
        <div class="info-box">
            <strong>测试账号:</strong> admin / admin123<br>
            <strong>说明:</strong> 使用管理员账号登录并检查权限状态
        </div>
        <button class="test-button" onclick="testAdminLogin()">管理员登录</button>
        <button class="test-button" onclick="testLogout()">退出登录</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🛡️ 权限验证测试</h2>
        <button class="test-button" onclick="testPermissions()">测试权限验证</button>
        <div id="permission-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔄 路由跳转测试</h2>
        <button class="test-button" onclick="testRouteNavigation()">测试反馈管理跳转</button>
        <div id="route-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        // 获取当前认证状态
        async function checkCurrentState() {
            const resultDiv = document.getElementById('state-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在检查当前状态...';
            
            try {
                // 检查localStorage和sessionStorage
                const token = sessionStorage.getItem('token') || localStorage.getItem('token');
                const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || localStorage.getItem('userInfo') || 'null');
                
                let statusText = '📊 前端存储状态:\n';
                statusText += `Token: ${token ? '✅ 存在' : '❌ 不存在'}\n`;
                statusText += `用户信息: ${userInfo ? '✅ 存在' : '❌ 不存在'}\n`;
                
                if (userInfo) {
                    statusText += `用户名: ${userInfo.username || '未知'}\n`;
                    statusText += `角色: ${userInfo.role || '未知'}\n`;
                    statusText += `是否管理员: ${userInfo.role === 'admin' ? '✅ 是' : '❌ 否'}\n`;
                }
                
                // 测试API连接
                if (token) {
                    try {
                        const response = await fetch(`${API_BASE}/auth/profile`, {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Accept': 'application/json'
                            }
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            const user = data.user || data;
                            statusText += `\n🌐 API验证状态:\n`;
                            statusText += `API连接: ✅ 正常\n`;
                            statusText += `用户ID: ${user.id}\n`;
                            statusText += `用户名: ${user.username}\n`;
                            statusText += `角色: ${user.role}\n`;
                            statusText += `管理员权限: ${user.role === 'admin' ? '✅ 有权限' : '❌ 无权限'}\n`;
                        } else {
                            statusText += `\n🌐 API验证状态:\n`;
                            statusText += `API连接: ❌ 失败 (${response.status})\n`;
                        }
                    } catch (error) {
                        statusText += `\n🌐 API验证状态:\n`;
                        statusText += `API连接: ❌ 网络错误 (${error.message})\n`;
                    }
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = statusText;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `检查状态失败: ${error.message}`;
            }
        }
        
        // 测试管理员登录
        async function testAdminLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在尝试管理员登录...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123',
                        email: '<EMAIL>',
                        name: '系统管理员'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    // 保存到sessionStorage
                    sessionStorage.setItem('token', data.token);
                    sessionStorage.setItem('userInfo', JSON.stringify(data.user));
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 管理员登录成功!\n` +
                        `用户名: ${data.user.username}\n` +
                        `角色: ${data.user.role}\n` +
                        `管理员权限: ${data.user.role === 'admin' ? '✅ 有权限' : '❌ 无权限'}\n` +
                        `Token: ${data.token.substring(0, 20)}...\n\n` +
                        `✅ 已保存到sessionStorage`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录失败: ${data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 登录失败: ${error.message}`;
            }
        }
        
        // 测试退出登录
        async function testLogout() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在退出登录...';
            
            try {
                // 清除存储
                sessionStorage.removeItem('token');
                sessionStorage.removeItem('userInfo');
                localStorage.removeItem('token');
                localStorage.removeItem('userInfo');
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 已退出登录，清除所有认证信息';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 退出失败: ${error.message}`;
            }
        }
        
        // 测试权限验证
        async function testPermissions() {
            const resultDiv = document.getElementById('permission-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试权限验证...';
            
            try {
                const token = sessionStorage.getItem('token');
                if (!token) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 未登录，请先进行管理员登录';
                    return;
                }
                
                // 测试需要管理员权限的API
                const adminAPIs = [
                    { name: '反馈管理', url: '/feedback' },
                    { name: '用户管理', url: '/users' }
                ];
                
                let resultText = '🛡️ 权限验证结果:\n\n';
                
                for (const api of adminAPIs) {
                    try {
                        const response = await fetch(`${API_BASE}${api.url}`, {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Accept': 'application/json'
                            }
                        });
                        
                        if (response.ok) {
                            resultText += `✅ ${api.name}: 权限验证通过 (${response.status})\n`;
                        } else {
                            const errorData = await response.json();
                            resultText += `❌ ${api.name}: 权限验证失败 (${response.status}) - ${errorData.message}\n`;
                        }
                    } catch (error) {
                        resultText += `❌ ${api.name}: 网络错误 - ${error.message}\n`;
                    }
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = resultText;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 权限测试失败: ${error.message}`;
            }
        }
        
        // 测试路由跳转
        async function testRouteNavigation() {
            const resultDiv = document.getElementById('route-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试路由跳转...';
            
            try {
                const token = sessionStorage.getItem('token');
                const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || 'null');
                
                let resultText = '🔄 路由跳转测试:\n\n';
                
                // 检查认证状态
                resultText += `认证状态: ${token ? '✅ 已登录' : '❌ 未登录'}\n`;
                resultText += `用户信息: ${userInfo ? '✅ 存在' : '❌ 不存在'}\n`;
                
                if (userInfo) {
                    resultText += `用户角色: ${userInfo.role}\n`;
                    resultText += `管理员权限: ${userInfo.role === 'admin' ? '✅ 有权限' : '❌ 无权限'}\n`;
                }
                
                // 模拟路由守卫逻辑
                const requiresAuth = true;
                const requiresAdmin = true;
                
                resultText += `\n🛡️ 路由守卫检查:\n`;
                resultText += `需要登录: ${requiresAuth ? '是' : '否'}\n`;
                resultText += `需要管理员权限: ${requiresAdmin ? '是' : '否'}\n`;
                
                if (requiresAuth && !token) {
                    resultText += `结果: ❌ 跳转到登录页 (未登录)\n`;
                } else if (requiresAdmin && (!userInfo || userInfo.role !== 'admin')) {
                    resultText += `结果: ❌ 跳转到首页 (权限不足)\n`;
                    resultText += `\n🔧 问题分析:\n`;
                    if (!userInfo) {
                        resultText += `- 用户信息丢失，可能是获取用户资料失败\n`;
                    } else if (userInfo.role !== 'admin') {
                        resultText += `- 用户角色不是admin，当前角色: ${userInfo.role}\n`;
                    }
                } else {
                    resultText += `结果: ✅ 允许访问反馈管理页面\n`;
                }
                
                // 提供修复建议
                if (token && (!userInfo || userInfo.role !== 'admin')) {
                    resultText += `\n💡 修复建议:\n`;
                    resultText += `1. 检查用户资料获取API是否正常\n`;
                    resultText += `2. 确认用户角色数据是否正确保存\n`;
                    resultText += `3. 验证前端认证store的状态管理\n`;
                }
                
                resultDiv.className = token && userInfo && userInfo.role === 'admin' ? 'result success' : 'result error';
                resultDiv.textContent = resultText;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 路由测试失败: ${error.message}`;
            }
        }
        
        // 页面加载时自动检查状态
        window.onload = () => {
            checkCurrentState();
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            width: 100%;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.error {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #6c757d;
        }
        .status-bar {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        .status-item {
            flex: 1;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
        .quick-actions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .quick-actions button {
            margin: 5px;
            padding: 10px 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 最终诊断工具</h1>
        <p>彻底解决前端错误和权限问题</p>
    </div>

    <div class="status-bar">
        <div class="status-item">
            <div id="api-status"><span class="status-indicator status-loading"></span>API连接: 检测中...</div>
        </div>
        <div class="status-item">
            <div id="auth-status"><span class="status-indicator status-loading"></span>认证状态: 检测中...</div>
        </div>
        <div class="status-item">
            <div id="permission-status"><span class="status-indicator status-loading"></span>权限验证: 检测中...</div>
        </div>
    </div>

    <div class="quick-actions">
        <button class="test-button" onclick="runFullDiagnosis()">🚀 运行完整诊断</button>
        <button class="test-button" onclick="adminLogin()">🔑 管理员登录</button>
        <button class="test-button" onclick="testFeedbackAccess()">📝 测试反馈管理</button>
        <button class="test-button" onclick="clearAllCache()">🧹 清除所有缓存</button>
    </div>

    <div class="test-grid">
        <div class="test-section">
            <h3>🔌 API连接测试</h3>
            <button class="test-button" onclick="testAPIConnection()">测试API连接</button>
            <div id="api-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 认证系统测试</h3>
            <button class="test-button" onclick="testAuthentication()">测试认证系统</button>
            <div id="auth-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📦 数据加载测试</h3>
            <button class="test-button" onclick="testDataLoading()">测试数据加载</button>
            <div id="data-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🛡️ 权限验证测试</h3>
            <button class="test-button" onclick="testPermissions()">测试权限验证</button>
            <div id="permission-result" class="result"></div>
        </div>
    </div>

    <script>
        // 更新状态指示器
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            if (element) {
                const indicator = element.querySelector('.status-indicator');
                const statusClasses = ['status-success', 'status-error', 'status-loading'];
                indicator.classList.remove(...statusClasses);
                indicator.classList.add(`status-${status}`);
                element.innerHTML = `<span class="status-indicator status-${status}"></span>${text}`;
            }
        }

        // 管理员登录
        async function adminLogin() {
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123',
                        email: '<EMAIL>',
                        name: '系统管理员'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    sessionStorage.setItem('token', data.token);
                    alert('✅ 管理员登录成功！');
                    updateStatusBar();
                } else {
                    alert('❌ 登录失败');
                }
            } catch (error) {
                alert('❌ 登录错误: ' + error.message);
            }
        }

        // 测试反馈管理访问
        async function testFeedbackAccess() {
            const token = sessionStorage.getItem('token');
            if (!token) {
                alert('⚠️ 请先登录');
                return;
            }

            try {
                const response = await fetch('/api/feedback', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    alert('✅ 反馈管理API访问成功！现在可以尝试访问反馈管理页面');
                    // 尝试打开反馈管理页面
                    window.open('http://localhost:5173/#/admin/feedback', '_blank');
                } else {
                    alert('❌ 反馈管理API访问失败: ' + response.status);
                }
            } catch (error) {
                alert('❌ 访问错误: ' + error.message);
            }
        }

        // 清除所有缓存
        function clearAllCache() {
            sessionStorage.clear();
            localStorage.clear();
            alert('✅ 所有缓存已清除，请刷新页面');
            location.reload();
        }

        // 测试API连接
        async function testAPIConnection() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试API连接...';

            const tests = [
                { name: '健康检查', url: '/api/health' },
                { name: '商品列表', url: '/api/products?limit=3' },
                { name: '分类列表', url: '/api/categories' },
                { name: '商品统计', url: '/api/products/stats' }
            ];

            let results = '🔌 API连接测试:\n\n';
            let successCount = 0;

            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    if (response.ok) {
                        results += `✅ ${test.name}: 正常\n`;
                        successCount++;
                    } else {
                        results += `❌ ${test.name}: ${response.status}\n`;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: 网络错误\n`;
                }
            }

            results += `\n📊 成功率: ${successCount}/${tests.length}`;
            resultDiv.className = successCount === tests.length ? 'result success' : 'result error';
            resultDiv.textContent = results;

            updateStatus('api-status', successCount === tests.length ? 'success' : 'error', 
                `API连接: ${successCount === tests.length ? '✅ 正常' : '❌ 异常'}`);
        }

        // 测试认证系统
        async function testAuthentication() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试认证系统...';

            let results = '🔐 认证系统测试:\n\n';
            const token = sessionStorage.getItem('token');

            if (!token) {
                results += '❌ 未登录\n建议: 点击"管理员登录"按钮';
                resultDiv.className = 'result error';
                resultDiv.textContent = results;
                updateStatus('auth-status', 'error', '认证状态: ❌ 未登录');
                return;
            }

            try {
                const response = await fetch('/api/auth/profile', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    const user = data.user || data;
                    results += `✅ 认证成功\n`;
                    results += `用户: ${user.username}\n`;
                    results += `角色: ${user.role}\n`;
                    results += `管理员: ${user.role === 'admin' ? '是' : '否'}`;
                    
                    resultDiv.className = 'result success';
                    updateStatus('auth-status', 'success', `认证状态: ✅ ${user.username}`);
                } else {
                    results += `❌ 认证失败: ${response.status}`;
                    resultDiv.className = 'result error';
                    updateStatus('auth-status', 'error', '认证状态: ❌ Token无效');
                }
            } catch (error) {
                results += `❌ 认证错误: ${error.message}`;
                resultDiv.className = 'result error';
                updateStatus('auth-status', 'error', '认证状态: ❌ 网络错误');
            }

            resultDiv.textContent = results;
        }

        // 测试数据加载
        async function testDataLoading() {
            const resultDiv = document.getElementById('data-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试数据加载...';

            const tests = [
                { name: '商品数据', url: '/api/products?limit=5' },
                { name: '分类数据', url: '/api/categories' },
                { name: '热门商品', url: '/api/products/hot?limit=3' },
                { name: '最新商品', url: '/api/products/recent?limit=3' }
            ];

            let results = '📦 数据加载测试:\n\n';
            let successCount = 0;

            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    if (response.ok) {
                        const data = await response.json();
                        let count = 0;
                        if (data.data && Array.isArray(data.data)) {
                            count = data.data.length;
                        } else if (Array.isArray(data)) {
                            count = data.length;
                        }
                        results += `✅ ${test.name}: ${count}条记录\n`;
                        successCount++;
                    } else {
                        results += `❌ ${test.name}: ${response.status}\n`;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: 网络错误\n`;
                }
            }

            results += `\n📊 成功率: ${successCount}/${tests.length}`;
            resultDiv.className = successCount === tests.length ? 'result success' : 'result error';
            resultDiv.textContent = results;
        }

        // 测试权限验证
        async function testPermissions() {
            const resultDiv = document.getElementById('permission-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试权限验证...';

            const token = sessionStorage.getItem('token');
            if (!token) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 未登录，无法测试权限';
                updateStatus('permission-status', 'error', '权限验证: ❌ 未登录');
                return;
            }

            const adminAPIs = [
                { name: '反馈管理', url: '/api/feedback' },
                { name: '用户管理', url: '/api/users' },
                { name: '系统设置', url: '/api/system/settings' }
            ];

            let results = '🛡️ 权限验证测试:\n\n';
            let successCount = 0;

            for (const api of adminAPIs) {
                try {
                    const response = await fetch(api.url, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });

                    if (response.ok) {
                        results += `✅ ${api.name}: 权限通过\n`;
                        successCount++;
                    } else {
                        results += `❌ ${api.name}: ${response.status}\n`;
                    }
                } catch (error) {
                    results += `❌ ${api.name}: 网络错误\n`;
                }
            }

            results += `\n📊 权限通过率: ${successCount}/${adminAPIs.length}`;
            resultDiv.className = successCount === adminAPIs.length ? 'result success' : 'result error';
            resultDiv.textContent = results;

            updateStatus('permission-status', successCount === adminAPIs.length ? 'success' : 'error',
                `权限验证: ${successCount === adminAPIs.length ? '✅ 通过' : '❌ 失败'}`);
        }

        // 运行完整诊断
        async function runFullDiagnosis() {
            await testAPIConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testDataLoading();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testPermissions();
        }

        // 更新状态栏
        async function updateStatusBar() {
            await testAPIConnection();
            await testAuthentication();
            await testPermissions();
        }

        // 页面加载时自动运行诊断
        window.onload = () => {
            updateStatusBar();
        };
    </script>
</body>
</html>

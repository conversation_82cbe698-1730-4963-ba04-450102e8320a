<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>🔍 API连接测试</h1>
    <p>测试前端与后端API的连接状态</p>

    <div class="test-section">
        <h2>📊 基础API测试</h2>
        <button class="test-button" onclick="testAPI('/api/health', '健康检查')">健康检查</button>
        <button class="test-button" onclick="testAPI('/api/categories', '分类列表')">分类列表</button>
        <button class="test-button" onclick="testAPI('/api/products', '商品列表')">商品列表</button>
        <div id="basic-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📈 新增API测试</h2>
        <button class="test-button" onclick="testAPI('/api/products/stats', '商品统计')">商品统计</button>
        <button class="test-button" onclick="testAPI('/api/products/recent', '最新商品')">最新商品</button>
        <button class="test-button" onclick="testAPI('/api/products/popular', '热门商品')">热门商品</button>
        <div id="new-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔄 批量测试</h2>
        <button class="test-button" onclick="runAllTests()">运行所有测试</button>
        <div id="batch-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        async function testAPI(endpoint, name) {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = `正在测试 ${name}...`;
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                const duration = Date.now() - startTime;
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    let summary = '';
                    if (Array.isArray(data)) {
                        summary = `返回 ${data.length} 条记录`;
                    } else if (data.data && Array.isArray(data.data)) {
                        summary = `返回 ${data.data.length} 条记录`;
                    } else if (typeof data === 'object') {
                        const keys = Object.keys(data);
                        summary = `返回对象，包含字段: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`;
                    }
                    
                    resultDiv.textContent = `✅ ${name}: ${response.status} (${duration}ms)\n${summary}\n\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ${name}: ${response.status}\n错误信息: ${data.message || JSON.stringify(data)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ${name}: 网络错误\n错误信息: ${error.message}`;
            }
        }
        
        async function runAllTests() {
            const resultDiv = document.getElementById('batch-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在运行批量测试...';
            
            const tests = [
                { endpoint: '/api/health', name: '健康检查' },
                { endpoint: '/api/categories', name: '分类列表' },
                { endpoint: '/api/products', name: '商品列表' },
                { endpoint: '/api/products/stats', name: '商品统计' },
                { endpoint: '/api/products/recent', name: '最新商品' },
                { endpoint: '/api/products/popular', name: '热门商品' }
            ];
            
            const results = [];
            let successCount = 0;
            
            for (const test of tests) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(`${API_BASE}${test.endpoint}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const duration = Date.now() - startTime;
                    
                    if (response.ok) {
                        results.push(`✅ ${test.name}: ${response.status} (${duration}ms)`);
                        successCount++;
                    } else {
                        const data = await response.json();
                        results.push(`❌ ${test.name}: ${response.status} - ${data.message || '未知错误'}`);
                    }
                } catch (error) {
                    results.push(`❌ ${test.name}: 网络错误 - ${error.message}`);
                }
                
                // 添加小延迟
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            resultDiv.className = successCount === tests.length ? 'result success' : 'result error';
            resultDiv.textContent = `批量测试完成！\n成功: ${successCount}/${tests.length}\n\n详细结果:\n${results.join('\n')}`;
        }
        
        // 页面加载时自动运行健康检查
        window.onload = () => {
            testAPI('/api/health', '健康检查');
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #6c757d;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
    </style>
</head>
<body>
    <h1>🔧 快速修复验证</h1>
    <p>验证API配置修复和认证状态修复的效果</p>

    <div class="test-section">
        <h2>🔍 API连接测试</h2>
        <button class="test-button" onclick="testAPIConnection()">测试API连接</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔐 完整登录流程测试</h2>
        <button class="test-button" onclick="testFullLoginFlow()">完整登录测试</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 实时状态监控</h2>
        <div id="status-monitor">
            <div><span class="status-indicator status-loading"></span>API连接: 检测中...</div>
            <div><span class="status-indicator status-loading"></span>认证状态: 检测中...</div>
            <div><span class="status-indicator status-loading"></span>权限验证: 检测中...</div>
        </div>
        <button class="test-button" onclick="startMonitoring()">开始监控</button>
        <button class="test-button" onclick="stopMonitoring()">停止监控</button>
    </div>

    <script>
        let monitoringInterval = null;
        
        // 测试API连接
        async function testAPIConnection() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试API连接...';
            
            const tests = [
                { name: '健康检查', url: '/api/health' },
                { name: '分类列表', url: '/api/categories' },
                { name: '商品列表', url: '/api/products?limit=5' },
                { name: '商品统计', url: '/api/products/stats' },
                { name: '最新商品', url: '/api/products/recent?limit=3' }
            ];
            
            let results = '🔌 API连接测试结果:\n\n';
            let allSuccess = true;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' }
                    });
                    
                    if (response.ok) {
                        results += `✅ ${test.name}: ${response.status}\n`;
                    } else {
                        results += `❌ ${test.name}: ${response.status}\n`;
                        allSuccess = false;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: 网络错误\n`;
                    allSuccess = false;
                }
            }
            
            results += `\n📊 总体状态: ${allSuccess ? '✅ 全部正常' : '⚠️ 存在问题'}`;
            
            resultDiv.className = allSuccess ? 'result success' : 'result error';
            resultDiv.textContent = results;
        }
        
        // 测试完整登录流程
        async function testFullLoginFlow() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试完整登录流程...';
            
            let results = '🔐 完整登录流程测试:\n\n';
            let success = true;
            
            try {
                // 1. 清除现有认证
                sessionStorage.removeItem('token');
                results += '1. ✅ 清除现有认证\n';
                
                // 2. 尝试管理员登录
                results += '2. 🔑 尝试管理员登录...\n';
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123',
                        email: '<EMAIL>',
                        name: '系统管理员'
                    })
                });
                
                if (!loginResponse.ok) {
                    throw new Error(`登录失败: ${loginResponse.status}`);
                }
                
                const loginData = await loginResponse.json();
                results += `   ✅ 登录成功，用户: ${loginData.user.username}, 角色: ${loginData.user.role}\n`;
                
                // 3. 保存token
                sessionStorage.setItem('token', loginData.token);
                results += '   ✅ Token已保存\n';
                
                // 4. 测试用户资料获取
                results += '3. 👤 测试用户资料获取...\n';
                const profileResponse = await fetch('/api/auth/profile', {
                    headers: { 'Authorization': `Bearer ${loginData.token}` }
                });
                
                if (!profileResponse.ok) {
                    throw new Error(`获取用户资料失败: ${profileResponse.status}`);
                }
                
                const profileData = await profileResponse.json();
                const user = profileData.user || profileData;
                results += `   ✅ 用户资料获取成功，角色: ${user.role}\n`;
                
                // 5. 测试管理员API访问
                results += '4. 🛡️ 测试管理员API访问...\n';
                const feedbackResponse = await fetch('/api/feedback', {
                    headers: { 'Authorization': `Bearer ${loginData.token}` }
                });
                
                if (!feedbackResponse.ok) {
                    throw new Error(`反馈管理API访问失败: ${feedbackResponse.status}`);
                }
                
                results += '   ✅ 反馈管理API访问成功\n';
                
                // 6. 权限验证总结
                results += '\n🎯 权限验证结果:\n';
                results += `   认证状态: ✅ 已登录\n`;
                results += `   用户角色: ${user.role}\n`;
                results += `   管理员权限: ${user.role === 'admin' ? '✅ 有权限' : '❌ 无权限'}\n`;
                results += `   API访问: ✅ 正常\n`;
                
                if (user.role !== 'admin') {
                    success = false;
                }
                
            } catch (error) {
                results += `❌ 测试失败: ${error.message}\n`;
                success = false;
            }
            
            results += `\n📊 流程状态: ${success ? '✅ 完全正常' : '⚠️ 存在问题'}`;
            
            resultDiv.className = success ? 'result success' : 'result error';
            resultDiv.textContent = results;
        }
        
        // 更新状态指示器
        function updateStatusIndicator(selector, status, text) {
            const element = document.querySelector(selector);
            if (element) {
                const indicator = element.querySelector('.status-indicator');
                const statusClasses = ['status-success', 'status-error', 'status-loading'];
                indicator.classList.remove(...statusClasses);
                indicator.classList.add(`status-${status}`);
                element.innerHTML = `<span class="status-indicator status-${status}"></span>${text}`;
            }
        }
        
        // 检查单个状态
        async function checkStatus() {
            // 检查API连接
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    updateStatusIndicator('#status-monitor div:nth-child(1)', 'success', 'API连接: ✅ 正常');
                } else {
                    updateStatusIndicator('#status-monitor div:nth-child(1)', 'error', 'API连接: ❌ 异常');
                }
            } catch (error) {
                updateStatusIndicator('#status-monitor div:nth-child(1)', 'error', 'API连接: ❌ 网络错误');
            }
            
            // 检查认证状态
            const token = sessionStorage.getItem('token');
            if (token) {
                try {
                    const response = await fetch('/api/auth/profile', {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    if (response.ok) {
                        const data = await response.json();
                        const user = data.user || data;
                        updateStatusIndicator('#status-monitor div:nth-child(2)', 'success', `认证状态: ✅ 已登录 (${user.username})`);
                        
                        // 检查权限
                        if (user.role === 'admin') {
                            updateStatusIndicator('#status-monitor div:nth-child(3)', 'success', '权限验证: ✅ 管理员权限');
                        } else {
                            updateStatusIndicator('#status-monitor div:nth-child(3)', 'error', `权限验证: ❌ 普通用户 (${user.role})`);
                        }
                    } else {
                        updateStatusIndicator('#status-monitor div:nth-child(2)', 'error', '认证状态: ❌ Token无效');
                        updateStatusIndicator('#status-monitor div:nth-child(3)', 'error', '权限验证: ❌ 未认证');
                    }
                } catch (error) {
                    updateStatusIndicator('#status-monitor div:nth-child(2)', 'error', '认证状态: ❌ 验证失败');
                    updateStatusIndicator('#status-monitor div:nth-child(3)', 'error', '权限验证: ❌ 验证失败');
                }
            } else {
                updateStatusIndicator('#status-monitor div:nth-child(2)', 'error', '认证状态: ❌ 未登录');
                updateStatusIndicator('#status-monitor div:nth-child(3)', 'error', '权限验证: ❌ 未登录');
            }
        }
        
        // 开始监控
        function startMonitoring() {
            if (monitoringInterval) return;
            
            checkStatus(); // 立即检查一次
            monitoringInterval = setInterval(checkStatus, 3000); // 每3秒检查一次
        }
        
        // 停止监控
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
        }
        
        // 页面加载时自动开始监控
        window.onload = () => {
            startMonitoring();
        };
        
        // 页面卸载时停止监控
        window.onbeforeunload = () => {
            stopMonitoring();
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>🚨 紧急API测试</h1>
    <p>诊断前端API调用问题</p>

    <div class="test-section">
        <h2>🔍 基础连接测试</h2>
        <button class="test-button" onclick="testBasicConnection()">测试基础连接</button>
        <div id="basic-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📦 商品API测试</h2>
        <button class="test-button" onclick="testProductsAPI()">测试商品API</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🏷️ 分类API测试</h2>
        <button class="test-button" onclick="testCategoriesAPI()">测试分类API</button>
        <div id="categories-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔐 认证API测试</h2>
        <button class="test-button" onclick="testAuthAPI()">测试认证API</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🛠️ 修复建议</h2>
        <button class="test-button" onclick="generateFixSuggestions()">生成修复建议</button>
        <div id="fix-result" class="result"></div>
    </div>

    <script>
        // 测试基础连接
        async function testBasicConnection() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试基础连接...';
            
            const tests = [
                { name: '后端健康检查', url: 'http://localhost:3000/api/health' },
                { name: 'Vite代理健康检查', url: '/api/health' },
                { name: '前端服务', url: 'http://localhost:5173' }
            ];
            
            let results = '🔌 基础连接测试结果:\n\n';
            let allSuccess = true;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' }
                    });
                    
                    if (response.ok) {
                        const data = await response.text();
                        results += `✅ ${test.name}: ${response.status} - ${data.substring(0, 50)}...\n`;
                    } else {
                        results += `❌ ${test.name}: ${response.status} ${response.statusText}\n`;
                        allSuccess = false;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: 网络错误 - ${error.message}\n`;
                    allSuccess = false;
                }
            }
            
            resultDiv.className = allSuccess ? 'result success' : 'result error';
            resultDiv.textContent = results;
        }
        
        // 测试商品API
        async function testProductsAPI() {
            const resultDiv = document.getElementById('products-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试商品API...';
            
            const tests = [
                { name: '直接后端商品列表', url: 'http://localhost:3000/api/products?limit=5' },
                { name: 'Vite代理商品列表', url: '/api/products?limit=5' },
                { name: '商品统计', url: '/api/products/stats' },
                { name: '价格范围', url: '/api/products/price-ranges' }
            ];
            
            let results = '📦 商品API测试结果:\n\n';
            let successCount = 0;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        results += `✅ ${test.name}: ${response.status}\n`;
                        if (data.data && Array.isArray(data.data)) {
                            results += `   📊 返回 ${data.data.length} 条记录\n`;
                        } else if (Array.isArray(data)) {
                            results += `   📊 返回 ${data.length} 条记录\n`;
                        } else {
                            results += `   📊 返回数据类型: ${typeof data}\n`;
                        }
                        successCount++;
                    } else {
                        const errorText = await response.text();
                        results += `❌ ${test.name}: ${response.status} ${response.statusText}\n`;
                        results += `   💬 错误: ${errorText.substring(0, 100)}...\n`;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: 网络错误 - ${error.message}\n`;
                }
                results += '\n';
            }
            
            results += `📊 成功率: ${successCount}/${tests.length}`;
            
            resultDiv.className = successCount === tests.length ? 'result success' : 'result error';
            resultDiv.textContent = results;
        }
        
        // 测试分类API
        async function testCategoriesAPI() {
            const resultDiv = document.getElementById('categories-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试分类API...';
            
            const tests = [
                { name: '直接后端分类列表', url: 'http://localhost:3000/api/categories' },
                { name: 'Vite代理分类列表', url: '/api/categories' }
            ];
            
            let results = '🏷️ 分类API测试结果:\n\n';
            let successCount = 0;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        results += `✅ ${test.name}: ${response.status}\n`;
                        if (Array.isArray(data)) {
                            results += `   📊 返回 ${data.length} 个分类\n`;
                            if (data.length > 0) {
                                results += `   📝 示例: ${data[0].name || '未知'}\n`;
                            }
                        }
                        successCount++;
                    } else {
                        const errorText = await response.text();
                        results += `❌ ${test.name}: ${response.status} ${response.statusText}\n`;
                        results += `   💬 错误: ${errorText.substring(0, 100)}...\n`;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: 网络错误 - ${error.message}\n`;
                }
                results += '\n';
            }
            
            results += `📊 成功率: ${successCount}/${tests.length}`;
            
            resultDiv.className = successCount === tests.length ? 'result success' : 'result error';
            resultDiv.textContent = results;
        }
        
        // 测试认证API
        async function testAuthAPI() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试认证API...';
            
            let results = '🔐 认证API测试结果:\n\n';
            
            // 检查当前认证状态
            const token = sessionStorage.getItem('token');
            results += `当前Token状态: ${token ? '✅ 存在' : '❌ 不存在'}\n\n`;
            
            if (token) {
                try {
                    const response = await fetch('/api/auth/profile', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Accept': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const user = data.user || data;
                        results += `✅ 用户资料获取成功\n`;
                        results += `   用户名: ${user.username}\n`;
                        results += `   角色: ${user.role}\n`;
                        results += `   管理员权限: ${user.role === 'admin' ? '✅ 有' : '❌ 无'}\n`;
                    } else {
                        results += `❌ 用户资料获取失败: ${response.status}\n`;
                    }
                } catch (error) {
                    results += `❌ 认证API错误: ${error.message}\n`;
                }
            } else {
                results += `⚠️ 未登录，无法测试认证API\n`;
            }
            
            resultDiv.className = 'result success';
            resultDiv.textContent = results;
        }
        
        // 生成修复建议
        async function generateFixSuggestions() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在分析问题并生成修复建议...';
            
            let suggestions = '🛠️ 修复建议:\n\n';
            
            // 测试API连接
            let apiWorking = false;
            try {
                const response = await fetch('/api/health');
                apiWorking = response.ok;
            } catch (error) {
                // API不工作
            }
            
            if (!apiWorking) {
                suggestions += '❌ API连接问题:\n';
                suggestions += '   1. 检查后端服务是否运行在端口3000\n';
                suggestions += '   2. 检查Vite代理配置是否正确\n';
                suggestions += '   3. 重启前端和后端服务\n\n';
            } else {
                suggestions += '✅ API连接正常\n\n';
            }
            
            // 检查认证状态
            const token = sessionStorage.getItem('token');
            if (!token) {
                suggestions += '⚠️ 认证问题:\n';
                suggestions += '   1. 用户未登录，请先登录\n';
                suggestions += '   2. 检查登录功能是否正常\n\n';
            } else {
                suggestions += '✅ 用户已登录\n\n';
            }
            
            // 通用修复建议
            suggestions += '🔧 通用修复步骤:\n';
            suggestions += '1. 清除浏览器缓存 (Ctrl+Shift+R)\n';
            suggestions += '2. 检查浏览器控制台错误信息\n';
            suggestions += '3. 重启开发服务器: ./restart.sh\n';
            suggestions += '4. 检查网络连接\n';
            suggestions += '5. 确认端口3000和5173没有被其他程序占用\n\n';
            
            suggestions += '📞 如果问题仍然存在:\n';
            suggestions += '1. 提供浏览器控制台的具体错误信息\n';
            suggestions += '2. 检查后端日志文件\n';
            suggestions += '3. 尝试直接访问 http://localhost:3000/api/health\n';
            
            resultDiv.className = 'result success';
            resultDiv.textContent = suggestions;
        }
        
        // 页面加载时自动运行基础测试
        window.onload = () => {
            testBasicConnection();
        };
    </script>
</body>
</html>

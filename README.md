# 光年小卖部 - 商城平台 | Light Year Store - E-commerce Platform

[English Version](#english-version)

## 项目简介

光年小卖部是一个基于Vue 3和Express构建的B2C电商平台，支持商品管理、内容发布、用户管理、福利兑换和数据分析等功能，目标是为企业提供完整的电子商务和福利兑换解决方案。

## 技术栈
- **前端**：Vue 3.3.4, Element Plus 2.3.8, Pinia 3.0.2, Vue Router 4.5.0
- **后端**：Express 4.18.2, Sequelize 6.33.0, MySQL/SQLite
- **构建工具**：Vite 4.4.0
- **数据可视化**：ECharts 5
- **富文本编辑**：WangEditor 5.1.23
- **HTTP请求**：Axios 1.8.4
- **文件上传**：Multer 1.4.5-lts.1
- **认证授权**：JWT (jsonwebtoken 9.0.2), bcryptjs 2.4.3, 飞书OAuth登录
- **日志记录**：Morgan 1.10.0
- **数据导入导出**：csv-parser 3.2.0, csv-writer 1.6.0, ExcelJS 4.4.0

## 开发环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- MySQL 或 SQLite (开发环境)

## 项目结构
```
项目根目录/
├── src/                   # 前端源码
│   ├── api/               # API请求封装
│   ├── components/        # 通用组件
│   │   └── admin/         # 管理后台组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面视图
│   │   ├── admin/         # 管理后台页面
│   │   └── user/          # 用户中心页面
│   ├── App.vue            # 根组件
│   └── main.js            # 应用入口
│
├── server/                # 后端源码
│   ├── config/            # 配置文件
│   ├── controllers/       # 业务控制器
│   ├── middlewares/       # 中间件
│   ├── models/            # 数据模型
│   ├── routes/            # 路由定义
│   ├── services/          # 业务服务
│   ├── utils/             # 工具函数
│   ├── migrations/        # 数据库迁移文件
│   ├── seeders/           # 数据库种子文件
│   ├── uploads/           # 文件上传目录
│   │   └── images/        # 图片存储目录
│   ├── scripts/           # 脚本工具
│   ├── logs/              # 日志目录
│   └── server.js          # 服务入口
```

## 快速开始

### 安装依赖
```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd server && npm install
```

### 开发模式运行
```bash
# 启动前端开发服务器
npm run dev

# 启动后端服务器(在另一个终端)
cd server && npm run dev
```

### 初始化数据库
```bash
cd server
npm run init-db
```

### 构建生产版本
```bash
npm run build
```

## 主要功能

### 商品管理系统
- **商品列表与查询**：支持多条件筛选、分页和排序
- **商品详情管理**：编辑商品名称、描述、价格、库存等信息
- **商品分类**：支持多级分类管理
- **商品图片**：支持多图上传、预览和删除
- **商品状态**：支持上架/下架管理
- **热门与新品标记**：突出显示热门商品和新上架商品
- **库存管理**：自动库存追踪，低库存告警
- **数据导入导出**：支持CSV/Excel格式批量导入导出商品数据

### 兑换与福利系统
- **兑换申请**：用户可使用光年币或人民币兑换商品
- **申请流程管理**：完整的状态流转（待处理→已批准→已发货→已完成）
- **兑换记录**：用户可查看个人兑换历史
- **订单状态查询**：实时跟踪兑换申请处理状态
- **发货物流管理**：支持添加物流单号和物流公司
- **库存自动扣减**：兑换成功自动扣减商品库存
- **兑换统计**：商品兑换数量和趋势分析

### 用户与权限管理
- **用户认证**：支持登录、注册功能
- **飞书集成**：支持飞书OAuth登录，自动获取用户部门、邮箱等信息
- **角色权限**：管理员和普通用户角色区分
- **用户管理**：管理员可管理用户账号信息
- **个人中心**：用户可编辑个人信息
- **部门管理**：支持按部门组织用户
- **账户安全**：密码加密存储和验证

### 内容管理系统
- **公告发布**：支持富文本编辑的公告发布
- **内容展示**：首页轮播展示重要公告
- **内容排序**：支持调整公告顺序和显示状态
- **内容富媒体**：支持图片、链接等富媒体内容
- **历史记录**：公告发布和修改记录

### 数据统计与分析
- **商品数据**：商品兑换量、库存变化统计
- **用户数据**：用户活跃度、新增用户统计
- **趋势分析**：各类数据的时间趋势图表
- **数据可视化**：使用ECharts生成直观的统计图表
- **管理仪表盘**：核心数据一目了然

## 飞书登录集成

本系统集成了飞书登录功能，可以实现以下功能：

- **单点登录**：用户可以通过飞书账号直接登录系统
- **自动同步**：自动同步用户的基本信息、部门信息、邮箱等
- **部门层级**：自动获取并记录用户的完整部门路径信息

### 飞书应用配置

要启用飞书登录功能，需要先在[飞书开放平台](https://open.feishu.cn/)创建应用并配置以下权限：

1. **通讯录权限**：
   - 获取部门基础信息 `contact:department.base:readonly`
   - 获取部门组织架构信息 `contact:department.organize:readonly`
   - 获取用户基本信息 `contact:user.base:readonly`
   - 获取用户组织架构信息 `contact:user.department:readonly`
   - 获取用户邮箱信息 `contact:user.email:readonly`
   - 获取用户 user ID `contact:user.employee_id:readonly`
   - 获取用户 location info `contact:user.location:readonly` (For retrieving user's city/workplace)

2. **应用配置**：
   - 在应用配置中设置重定向URL为：`{YOUR_DOMAIN}/api/feishu/callback`
   - 将应用的AppID和AppSecret配置到系统的环境变量中

3. **权限申请流程**：
   - 权限申请后，需要提交版本发布申请
   - 企业管理员需要在飞书后台审核并通过应用权限

4. **设置可访问的数据范围**：
   - 除了获取API权限外，还需要设置应用可访问的部门范围
   - 在飞书开放平台 -> 开发者后台 -> 权限管理 -> 可访问的数据范围中设置
   - 建议设置为"全部部门和成员"，否则可能无法获取部门信息
   - 即使有API权限，如果没有设置数据范围，用户部门信息仍会显示为默认值

> **注意**：如果用户登录后部门显示不正确，请检查应用的"可访问的数据范围"设置是否包含用户所在部门。

## 📚 文档导航

### 📖 核心文档
- **[API文档](docs/API.md)** - 完整的API接口文档和Swagger UI
- **[部署指南](docs/DEPLOYMENT.md)** - 开发/生产环境部署详细说明
- **[开发规范](docs/DEVELOPMENT.md)** - 代码规范、提交规范和开发流程
- **[架构文档](ARCHITECTURE.md)** - 项目架构设计和技术选型

### 🔧 运维文档
- **[故障排除](docs/TROUBLESHOOTING.md)** - 常见问题诊断和解决方案
- **[维护指南](docs/MAINTENANCE.md)** - 日常维护任务和监控策略
- **[性能优化报告](PERFORMANCE_OPTIMIZATION_REPORT.md)** - 性能优化成果和建议

### 🌐 在线文档
- **Swagger API**: [http://localhost:3000/api-docs](http://localhost:3000/api-docs)
- **健康检查**: [http://localhost:3000/api/health](http://localhost:3000/api/health)
- **性能监控**: 运行 `node scripts/maintenance/performance-optimizer.js`

### 📋 历史文档
- [开发文档](./开发文档.md) - 详细的项目架构、数据库设计和API文档
- [部署与环境配置指南](./部署与环境配置指南.md) - 开发环境和生产环境的配置指南
- [飞书集成指南](./飞书集成指南.md) - 详细的飞书应用创建和配置步骤

## 版本历史

### v1.1.0 (2024年7月)
- 优化兑换流程
- 增加库存预警功能
- 改进数据统计图表
- 完善通知系统
- 添加飞书登录集成，支持部门信息同步

### v1.0.0 (2024年6月)
- 基础功能完成，包括用户、商品、内容和反馈系统
- 初始版本发布

## 维护者

如有问题，请联系项目维护者。

---

# English Version

## Project Description
Light Year Store is a B2C e-commerce platform built with Vue 3 and Express, supporting product management, content publishing, user management, and data analysis. The project aims to provide a complete e-commerce solution for businesses.

## Technology Stack
- **Frontend**: Vue 3.3.4, Element Plus 2.3.8, Pinia 3.0.2, Vue Router 4.5.0
- **Backend**: Express 4.18.2, Sequelize 6.33.0, MySQL/SQLite
- **Build Tool**: Vite 4.4.0
- **Data Visualization**: ECharts 5
- **Rich Text Editor**: WangEditor 5.1.23
- **HTTP Client**: Axios 1.8.4
- **File Upload**: Multer 1.4.5-lts.1
- **Authentication**: JWT (jsonwebtoken 9.0.2), bcryptjs 2.4.3, Feishu OAuth
- **Logging**: Morgan 1.10.0
- **Data Import/Export**: csv-parser 3.2.0, csv-writer 1.6.0, ExcelJS 4.4.0

## Development Environment Requirements
- Node.js >= 14.0.0
- npm >= 6.0.0
- MySQL or SQLite (development)

## Quick Start

### Install Dependencies
```bash
# Install frontend dependencies
npm install

# Install backend dependencies
cd server && npm install
```

### Run Development Mode
```bash
# Start frontend development server
npm run dev

# Start backend server (in another terminal)
cd server && npm run dev
```

### Initialize Database
```bash
cd server
npm run init-db
```

### Build Production Version
```bash
npm run build
```

## Main Features

### Product Management System
- **Product Listing & Search**: Multi-condition filtering, pagination, and sorting
- **Product Details Management**: Edit product name, description, price, inventory, etc.
- **Product Categories**: Support for multi-level category management
- **Product Images**: Support for multiple image upload, preview, and deletion
- **Product Status**: Support for listing/delisting management
- **Featured & New Tags**: Highlight popular products and newly listed products
- **Inventory Management**: Automatic inventory tracking, low inventory alerts
- **Data Import/Export**: Support for CSV/Excel format batch import/export of product data

### Exchange & Welfare System
- **Exchange Applications**: Users can exchange products using Light Year coins or RMB
- **Application Process Management**: Complete status flow (pending→approved→shipped→completed)
- **Exchange Records**: Users can view personal exchange history
- **Order Status Query**: Real-time tracking of exchange application processing status
- **Shipping & Logistics Management**: Support for adding tracking numbers and logistics companies
- **Automatic Inventory Deduction**: Successful exchanges automatically reduce product inventory
- **Exchange Statistics**: Product exchange quantity and trend analysis

### User & Permission Management
- **User Authentication**: Support for login and registration functions
- **Feishu Integration**: Support for Feishu OAuth login, automatically getting user department and email info
- **Role Permissions**: Administrator and regular user role distinction
- **User Management**: Administrators can manage user account information
- **Personal Center**: Users can edit personal information
- **Department Management**: Support for organizing users by department
- **Account Security**: Password encryption storage and verification

### Content Management System
- **Announcement Publishing**: Support for rich text editing announcement publishing
- **Content Display**: Homepage carousel display of important announcements
- **Content Sorting**: Support for adjusting announcement order and display status
- **Rich Media Content**: Support for images, links, and other rich media content
- **History Records**: Announcement publishing and modification records

### Data Statistics & Analysis
- **Product Data**: Product exchange volume, inventory change statistics
- **User Data**: User activity, new user statistics
- **Trend Analysis**: Time trend charts for various data
- **Data Visualization**: Using ECharts to generate intuitive statistical charts
- **Management Dashboard**: Core data at a glance

## Feishu Login Integration

The system integrates Feishu login functionality with the following features:

- **Single Sign-On**: Users can log in directly using their Feishu account
- **Auto-Synchronization**: Automatically syncs user basic info, department info, email, etc.
- **Department Hierarchy**: Automatically retrieves and records complete department path information

### Feishu Application Configuration

To enable Feishu login functionality, you need to create an application on the [Feishu Open Platform](https://open.feishu.cn/) and configure the following permissions:

1. **Contact Directory Permissions**:
   - Get department basic info `contact:department.base:readonly`
   - Get department organization info `contact:department.organize:readonly`
   - Get user basic info `contact:user.base:readonly`
   - Get user organization info `contact:user.department:readonly`
   - Get user email info `contact:user.email:readonly`
   - Get user ID `contact:user.employee_id:readonly`
   - Get user location info `contact:user.location:readonly` (For retrieving user's city/workplace)

2. **Application Configuration**:
   - Set the redirect URL in the application configuration to: `{YOUR_DOMAIN}/api/feishu/callback`
   - Configure the AppID and AppSecret to the system's environment variables

3. **Permission Application Process**:
   - After requesting permissions, you need to submit a version release application
   - The enterprise administrator needs to review and approve application permissions in the Feishu admin panel

4. **Set Accessible Data Range**:
   - In addition to getting API permissions, you need to set the accessible department range for the application
   - Set it in the Feishu Open Platform -> Developer Backend -> Permission Management -> Accessible Data Range
   - It's recommended to set it to "All departments and members", otherwise the user department info may still show as default value

> **Note**: If the user department shows incorrectly after logging in, please check if the "Accessible Data Range" setting in the application includes the user's department.

## Documentation

- [Development Documentation](./开发文档.md) - Detailed project architecture, database design, and API documentation
- [Deployment & Environment Configuration Guide](./部署与环境配置指南.md) - Configuration guide for development and production environments
- [Feishu Integration Guide](./飞书集成指南.md) - Detailed Feishu application creation and configuration steps

## Version History

### v1.1.0 (July 2024)
- Optimized exchange process
- Added inventory warning function
- Improved statistical charts
- Enhanced notification system
- Added Feishu login integration with department information synchronization

### v1.0.0 (June 2024)
- Basic features completed, including user, product, content, and feedback systems
- Initial version release

## Maintainer

If you have any questions, please contact the project maintainer.

*Documentation updated: July 2024*
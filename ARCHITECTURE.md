# 光年小卖部 - 项目架构文档

## 📁 项目结构

```
项目根目录/
├── src/                          # 前端源码
│   ├── api/                      # API接口
│   ├── components/               # 公共组件
│   ├── router/                   # 路由管理
│   │   ├── modules/              # 路由模块
│   │   │   ├── admin.js          # 管理员路由
│   │   │   ├── user.js           # 用户路由
│   │   │   └── public.js         # 公共路由
│   │   ├── guards.js             # 路由守卫
│   │   └── index.js              # 路由入口
│   ├── stores/                   # 状态管理
│   ├── styles/                   # 样式文件
│   ├── utils/                    # 工具函数
│   └── views/                    # 页面组件
├── server/                       # 后端源码
│   ├── config/                   # 配置文件
│   ├── controllers/              # 控制器
│   ├── middlewares/              # 中间件
│   ├── models/                   # 数据模型
│   ├── routes/                   # 路由定义
│   │   └── index.js              # 统一路由管理
│   ├── services/                 # 业务服务
│   ├── utils/                    # 工具函数
│   └── server.js                 # 服务入口
├── config/                       # 项目配置
│   ├── nginx/                    # Nginx配置
│   ├── env/                      # 环境配置
│   ├── database/                 # 数据库配置
│   └── index.js                  # 统一配置管理
├── scripts/                      # 脚本工具
│   ├── build/                    # 构建脚本
│   ├── deploy/                   # 部署脚本
│   └── maintenance/              # 维护脚本
├── docs/                         # 项目文档
├── temp/                         # 临时文件
│   ├── logs/                     # 临时日志
│   ├── tests/                    # 测试文件
│   ├── uploads/                  # 临时上传
│   └── cache/                    # 缓存文件
└── dist/                         # 构建产物
```

## 🏗️ 架构设计

### 前端架构

#### 路由管理
- **模块化路由**: 按功能模块拆分路由配置
- **统一守卫**: 集中管理权限验证和页面标题
- **懒加载**: 所有页面组件采用动态导入

#### 状态管理
- **Pinia**: 使用Pinia进行状态管理
- **模块化**: 按业务功能拆分store
- **持久化**: 关键状态自动持久化

#### 组件设计
- **原子化**: 基础组件可复用
- **业务组件**: 封装特定业务逻辑
- **布局组件**: 统一页面布局

### 后端架构

#### 路由管理
- **统一注册**: 通过`routes/index.js`统一管理所有路由
- **自动发现**: 自动注册路由模块，减少手动配置
- **错误处理**: 统一的错误处理和日志记录

#### 中间件设计
- **认证中间件**: 统一的JWT认证
- **权限中间件**: 基于角色的权限控制
- **日志中间件**: 请求日志记录
- **错误中间件**: 统一错误处理

#### 服务层设计
- **业务逻辑**: 封装在service层
- **数据访问**: 通过ORM进行数据操作
- **缓存策略**: 合理使用缓存提升性能

## 🔧 配置管理

### 统一配置
- **环境配置**: 通过`config/index.js`统一管理
- **自动验证**: 启动时验证必要的环境变量
- **目录创建**: 自动创建必要的目录结构

### 环境变量
```bash
# 基础配置
NODE_ENV=production
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=feishu_mall
DB_USER=root
DB_PASSWORD=password

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h

# 飞书配置
FEISHU_APP_ID=your-app-id
FEISHU_APP_SECRET=your-app-secret
FEISHU_REDIRECT_URI=your-redirect-uri
```

## 🚀 部署架构

### 构建流程
1. **依赖安装**: 前后端依赖分别安装
2. **前端构建**: Vite构建生产版本
3. **资源复制**: 复制必要的静态资源
4. **配置生成**: 生成部署配置文件

### 部署脚本
- `scripts/build/build.sh`: 完整构建脚本
- `scripts/deploy/production-deploy.sh`: 生产环境部署
- `scripts/maintenance/`: 维护相关脚本

### 服务器配置
- **Nginx**: 反向代理和静态文件服务
- **PM2**: Node.js进程管理
- **MySQL**: 生产数据库

## 📝 开发规范

### 代码组织
- **模块化**: 按功能模块组织代码
- **单一职责**: 每个文件/函数职责单一
- **命名规范**: 使用有意义的命名

### 路由规范
- **RESTful**: API遵循RESTful设计
- **版本控制**: API版本通过路径管理
- **权限控制**: 明确的权限验证

### 错误处理
- **统一格式**: 错误响应格式统一
- **日志记录**: 详细的错误日志
- **用户友好**: 用户友好的错误提示

## 🔄 维护指南

### 日常维护
- **日志清理**: 定期清理过期日志
- **临时文件**: 清理临时文件目录
- **依赖更新**: 定期更新依赖包

### 监控指标
- **性能监控**: 响应时间、吞吐量
- **错误监控**: 错误率、异常统计
- **资源监控**: CPU、内存使用率

### 备份策略
- **数据库备份**: 定期数据库备份
- **文件备份**: 上传文件备份
- **配置备份**: 配置文件版本控制

## 📚 相关文档

- [API文档](./docs/API.md)
- [部署指南](./docs/DEPLOYMENT.md)
- [开发指南](./docs/DEVELOPMENT.md)
- [故障排除](./docs/TROUBLESHOOTING.md)

#!/bin/bash

# 临时文件清理脚本
# 用于定期清理 temp 目录中的过期文件

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${YELLOW}===== 临时文件清理脚本 =====${NC}"

# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMP_DIR="$SCRIPT_DIR"

echo -e "清理目录: $TEMP_DIR"

# 清理函数
cleanup_directory() {
    local dir="$1"
    local days="$2"
    local description="$3"
    
    if [ -d "$dir" ]; then
        echo -e "${YELLOW}正在清理 $description (保留 $days 天内的文件)...${NC}"
        
        # 查找并删除超过指定天数的文件
        local count=$(find "$dir" -type f -mtime +$days 2>/dev/null | wc -l)
        
        if [ $count -gt 0 ]; then
            find "$dir" -type f -mtime +$days -delete 2>/dev/null
            echo -e "${GREEN}已删除 $count 个过期文件${NC}"
        else
            echo -e "${GREEN}没有找到过期文件${NC}"
        fi
    else
        echo -e "${RED}目录不存在: $dir${NC}"
    fi
}

# 清理缓存文件（立即清理）
cleanup_cache() {
    local cache_dir="$TEMP_DIR/cache"
    if [ -d "$cache_dir" ]; then
        echo -e "${YELLOW}正在清理缓存文件...${NC}"
        local count=$(find "$cache_dir" -type f 2>/dev/null | wc -l)
        if [ $count -gt 0 ]; then
            rm -f "$cache_dir"/*
            echo -e "${GREEN}已清理 $count 个缓存文件${NC}"
        else
            echo -e "${GREEN}缓存目录已经是空的${NC}"
        fi
    fi
}

# 显示使用帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -a, --all       清理所有临时文件"
    echo "  -l, --logs      清理日志文件 (保留7天)"
    echo "  -t, --tests     清理测试文件 (保留3天)"
    echo "  -u, --uploads   清理上传文件 (保留1天)"
    echo "  -c, --cache     清理缓存文件 (立即清理)"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -a           # 清理所有临时文件"
    echo "  $0 -l -c        # 只清理日志和缓存"
}

# 解析命令行参数
CLEAN_ALL=false
CLEAN_LOGS=false
CLEAN_TESTS=false
CLEAN_UPLOADS=false
CLEAN_CACHE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -a|--all)
            CLEAN_ALL=true
            shift
            ;;
        -l|--logs)
            CLEAN_LOGS=true
            shift
            ;;
        -t|--tests)
            CLEAN_TESTS=true
            shift
            ;;
        -u|--uploads)
            CLEAN_UPLOADS=true
            shift
            ;;
        -c|--cache)
            CLEAN_CACHE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定任何选项，显示帮助
if [ "$CLEAN_ALL" = false ] && [ "$CLEAN_LOGS" = false ] && [ "$CLEAN_TESTS" = false ] && [ "$CLEAN_UPLOADS" = false ] && [ "$CLEAN_CACHE" = false ]; then
    echo -e "${YELLOW}请指定要清理的内容，或使用 -h 查看帮助${NC}"
    exit 1
fi

# 执行清理
if [ "$CLEAN_ALL" = true ]; then
    echo -e "${YELLOW}执行完整清理...${NC}"
    cleanup_directory "$TEMP_DIR/logs" 7 "日志文件"
    cleanup_directory "$TEMP_DIR/tests" 3 "测试文件"
    cleanup_directory "$TEMP_DIR/uploads" 1 "上传文件"
    cleanup_cache
else
    if [ "$CLEAN_LOGS" = true ]; then
        cleanup_directory "$TEMP_DIR/logs" 7 "日志文件"
    fi
    
    if [ "$CLEAN_TESTS" = true ]; then
        cleanup_directory "$TEMP_DIR/tests" 3 "测试文件"
    fi
    
    if [ "$CLEAN_UPLOADS" = true ]; then
        cleanup_directory "$TEMP_DIR/uploads" 1 "上传文件"
    fi
    
    if [ "$CLEAN_CACHE" = true ]; then
        cleanup_cache
    fi
fi

echo -e "${GREEN}清理完成！${NC}"

# 显示当前目录状态
echo -e "\n${YELLOW}当前目录状态:${NC}"
for subdir in logs tests uploads cache; do
    if [ -d "$TEMP_DIR/$subdir" ]; then
        local file_count=$(find "$TEMP_DIR/$subdir" -type f 2>/dev/null | wc -l)
        echo -e "  $subdir/: $file_count 个文件"
    fi
done

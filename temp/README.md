# 临时文件目录说明

## 📁 目录结构

此目录用于存放项目开发过程中产生的各种临时文件，避免项目根目录变得混乱。

### 子目录说明

#### `logs/` - 临时日志文件
- **用途**: 存放开发和测试过程中产生的临时日志文件
- **文件类型**: 
  - `*.log` - 应用运行日志
  - `*.out` - 输出日志
  - `*.err` - 错误日志
- **清理策略**: 建议每周清理，保留最近7天的日志

#### `tests/` - 测试临时文件
- **用途**: 存放测试脚本、测试数据和测试结果
- **文件类型**:
  - `test_*.js` - 测试脚本
  - `test_*.html` - 测试页面
  - `*.csv` - 测试数据文件
  - `test_results_*.json` - 测试结果
- **清理策略**: 测试完成后可立即删除，重要测试结果可保留

#### `uploads/` - 临时上传文件
- **用途**: 存放开发测试时的临时上传文件
- **文件类型**:
  - 图片文件 (`*.jpg`, `*.png`, `*.gif`)
  - 文档文件 (`*.pdf`, `*.doc`, `*.xlsx`)
  - 其他测试上传文件
- **清理策略**: 建议每天清理，避免占用过多磁盘空间

#### `cache/` - 缓存文件
- **用途**: 存放应用运行时产生的缓存文件
- **文件类型**:
  - `*.cache` - 通用缓存文件
  - `*.tmp` - 临时缓存
  - `session_*` - 会话缓存
- **清理策略**: 可设置自动清理，或每次重启应用时清理

## 🧹 清理建议

### 自动清理脚本
可以创建定时清理脚本，建议的清理频率：
- **每日清理**: `uploads/` 目录
- **每周清理**: `logs/` 目录（保留最近7天）
- **每月清理**: `tests/` 目录（保留重要测试结果）
- **重启时清理**: `cache/` 目录

### 手动清理命令
```bash
# 清理所有临时文件（谨慎使用）
rm -rf temp/*

# 按类型清理
rm -rf temp/logs/*     # 清理日志
rm -rf temp/tests/*    # 清理测试文件
rm -rf temp/uploads/*  # 清理上传文件
rm -rf temp/cache/*    # 清理缓存
```

## ⚠️ 注意事项

1. **版本控制**: 此目录已添加到 `.gitignore`，不会被提交到版本控制
2. **权限设置**: 目录权限设置为 755，确保应用有读写权限
3. **定期维护**: 建议定期清理，避免临时文件占用过多磁盘空间
4. **重要文件**: 不要在此目录存放重要的项目文件，仅用于临时文件

## 📝 使用示例

### 在代码中使用
```javascript
// Node.js 示例
const tempDir = path.join(__dirname, '../temp');
const logFile = path.join(tempDir, 'logs', 'app.log');
const testFile = path.join(tempDir, 'tests', 'test_data.json');
```

### 在脚本中使用
```bash
# 创建临时日志
echo "测试日志" > temp/logs/test_$(date +%Y%m%d_%H%M%S).log

# 创建测试文件
cp test_data.csv temp/tests/
```

---

**创建时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**维护者**: 项目开发团队  
**版本**: 1.0

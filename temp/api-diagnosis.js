#!/usr/bin/env node

/**
 * API诊断脚本
 * 测试前后端API连接和数据获取
 */

import axios from 'axios';

console.log('🔍 开始API诊断...\n');

// API基础配置
const API_BASE = 'http://localhost:3000/api';
const FRONTEND_URL = 'http://localhost:5173';

// 测试API端点列表
const testEndpoints = [
  { name: '健康检查', url: '/health', method: 'GET' },
  { name: '分类列表', url: '/categories', method: 'GET' },
  { name: '商品列表', url: '/products', method: 'GET' },
  { name: '商品统计', url: '/products/stats', method: 'GET' },
  { name: '热门商品', url: '/products/popular', method: 'GET' },
  { name: '最新商品', url: '/products/recent', method: 'GET' }
];

/**
 * 测试单个API端点
 */
async function testEndpoint(endpoint) {
  try {
    const startTime = Date.now();
    const response = await axios({
      method: endpoint.method,
      url: `${API_BASE}${endpoint.url}`,
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    const duration = Date.now() - startTime;
    
    console.log(`✅ ${endpoint.name}: ${response.status} (${duration}ms)`);
    
    // 显示数据摘要
    if (response.data) {
      if (Array.isArray(response.data)) {
        console.log(`   📊 返回 ${response.data.length} 条记录`);
      } else if (response.data.data && Array.isArray(response.data.data)) {
        console.log(`   📊 返回 ${response.data.data.length} 条记录`);
      } else if (typeof response.data === 'object') {
        const keys = Object.keys(response.data);
        console.log(`   📊 返回对象，包含字段: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`);
      }
    }
    
    return { success: true, status: response.status, duration };
    
  } catch (error) {
    console.log(`❌ ${endpoint.name}: ${error.response?.status || 'NETWORK_ERROR'}`);
    
    if (error.response?.data) {
      console.log(`   💬 错误信息: ${error.response.data.message || JSON.stringify(error.response.data)}`);
    } else if (error.message) {
      console.log(`   💬 错误信息: ${error.message}`);
    }
    
    return { 
      success: false, 
      status: error.response?.status || 0, 
      error: error.message 
    };
  }
}

/**
 * 测试前端服务器
 */
async function testFrontend() {
  try {
    const response = await axios.get(FRONTEND_URL, { timeout: 5000 });
    console.log(`✅ 前端服务器: ${response.status}`);
    return true;
  } catch (error) {
    console.log(`❌ 前端服务器: ${error.response?.status || 'NETWORK_ERROR'}`);
    return false;
  }
}

/**
 * 测试CORS配置
 */
async function testCORS() {
  try {
    const response = await axios.options(`${API_BASE}/categories`, {
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      },
      timeout: 5000
    });
    
    console.log(`✅ CORS预检: ${response.status}`);
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': response.headers['access-control-allow-origin'],
      'Access-Control-Allow-Methods': response.headers['access-control-allow-methods'],
      'Access-Control-Allow-Headers': response.headers['access-control-allow-headers']
    };
    
    console.log('   📋 CORS头信息:');
    Object.entries(corsHeaders).forEach(([key, value]) => {
      if (value) {
        console.log(`      ${key}: ${value}`);
      }
    });
    
    return true;
  } catch (error) {
    console.log(`❌ CORS预检: ${error.response?.status || 'NETWORK_ERROR'}`);
    return false;
  }
}

/**
 * 主诊断函数
 */
async function runDiagnosis() {
  console.log('🌐 服务器连接测试');
  console.log('==================');
  
  // 测试前端服务器
  const frontendOk = await testFrontend();
  console.log();
  
  // 测试CORS
  console.log('🔒 CORS配置测试');
  console.log('================');
  const corsOk = await testCORS();
  console.log();
  
  // 测试API端点
  console.log('🔌 API端点测试');
  console.log('===============');
  
  const results = [];
  for (const endpoint of testEndpoints) {
    const result = await testEndpoint(endpoint);
    results.push({ ...endpoint, ...result });
    
    // 添加小延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📊 诊断结果汇总');
  console.log('================');
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ 成功: ${successful}/${results.length}`);
  console.log(`❌ 失败: ${failed}/${results.length}`);
  
  if (failed > 0) {
    console.log('\n🚨 失败的端点:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`   - ${r.name}: ${r.status} ${r.error || ''}`);
    });
  }
  
  // 性能统计
  const successfulRequests = results.filter(r => r.success && r.duration);
  if (successfulRequests.length > 0) {
    const avgDuration = successfulRequests.reduce((sum, r) => sum + r.duration, 0) / successfulRequests.length;
    const maxDuration = Math.max(...successfulRequests.map(r => r.duration));
    const minDuration = Math.min(...successfulRequests.map(r => r.duration));
    
    console.log('\n⚡ 性能统计:');
    console.log(`   平均响应时间: ${avgDuration.toFixed(0)}ms`);
    console.log(`   最快响应: ${minDuration}ms`);
    console.log(`   最慢响应: ${maxDuration}ms`);
  }
  
  // 总体状态
  const overallStatus = frontendOk && corsOk && failed === 0;
  console.log(`\n🎯 总体状态: ${overallStatus ? '✅ 正常' : '⚠️ 存在问题'}`);
  
  if (!overallStatus) {
    console.log('\n🔧 修复建议:');
    if (!frontendOk) {
      console.log('   - 检查前端服务器是否正常运行 (http://localhost:5173)');
    }
    if (!corsOk) {
      console.log('   - 检查后端CORS配置');
    }
    if (failed > 0) {
      console.log('   - 检查后端API服务器状态');
      console.log('   - 验证数据库连接');
      console.log('   - 查看后端日志获取详细错误信息');
    }
  }
  
  return overallStatus;
}

// 运行诊断
runDiagnosis()
  .then(success => {
    console.log('\n✅ API诊断完成！');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 诊断过程中发生错误:', error.message);
    process.exit(1);
  });

#!/usr/bin/env node

/**
 * 管理员权限测试脚本
 * 测试管理员登录和权限验证
 */

import axios from 'axios';

console.log('🔐 开始管理员权限测试...\n');

const API_BASE = 'http://localhost:3000/api';

// 测试用的管理员账号
const adminCredentials = {
  username: 'admin',
  password: 'admin123',
  email: '<EMAIL>',
  name: '系统管理员'
};

/**
 * 管理员登录
 */
async function adminLogin() {
  try {
    console.log('🔑 尝试管理员登录...');
    
    const response = await axios.post(`${API_BASE}/auth/login`, adminCredentials);
    
    if (response.data.token) {
      console.log('✅ 管理员登录成功');
      console.log(`   用户名: ${response.data.user.username}`);
      console.log(`   角色: ${response.data.user.role}`);
      console.log(`   是否管理员: ${response.data.user.role === 'admin'}`);
      
      return response.data.token;
    } else {
      console.log('❌ 登录失败: 未返回token');
      return null;
    }
  } catch (error) {
    console.log('❌ 管理员登录失败');
    if (error.response?.data) {
      console.log(`   错误信息: ${error.response.data.message}`);
    } else {
      console.log(`   错误信息: ${error.message}`);
    }
    return null;
  }
}

/**
 * 测试管理员API访问
 */
async function testAdminAPIs(token) {
  const adminAPIs = [
    { name: '反馈管理', url: '/feedback' },
    { name: '用户管理', url: '/users' },
    { name: '系统设置', url: '/system/settings' },
    { name: '日志管理', url: '/logs' }
  ];
  
  console.log('\n🔍 测试管理员API访问权限...');
  
  const results = [];
  
  for (const api of adminAPIs) {
    try {
      const response = await axios.get(`${API_BASE}${api.url}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        },
        timeout: 5000
      });
      
      console.log(`✅ ${api.name}: ${response.status}`);
      
      // 显示数据摘要
      if (response.data) {
        if (Array.isArray(response.data)) {
          console.log(`   📊 返回 ${response.data.length} 条记录`);
        } else if (response.data.data && Array.isArray(response.data.data)) {
          console.log(`   📊 返回 ${response.data.data.length} 条记录`);
        } else if (typeof response.data === 'object') {
          const keys = Object.keys(response.data);
          console.log(`   📊 返回对象，包含字段: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`);
        }
      }
      
      results.push({ ...api, success: true, status: response.status });
      
    } catch (error) {
      console.log(`❌ ${api.name}: ${error.response?.status || 'NETWORK_ERROR'}`);
      
      if (error.response?.data) {
        console.log(`   💬 错误信息: ${error.response.data.message || JSON.stringify(error.response.data)}`);
      } else if (error.message) {
        console.log(`   💬 错误信息: ${error.message}`);
      }
      
      results.push({ 
        ...api, 
        success: false, 
        status: error.response?.status || 0,
        error: error.response?.data?.message || error.message
      });
    }
    
    // 添加小延迟
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  return results;
}

/**
 * 测试用户资料获取
 */
async function testUserProfile(token) {
  try {
    console.log('\n👤 获取用户资料...');
    
    const response = await axios.get(`${API_BASE}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      }
    });
    
    console.log('✅ 用户资料获取成功');

    // 处理不同的响应数据结构
    const userData = response.data.user || response.data;

    console.log(`   用户ID: ${userData.id}`);
    console.log(`   用户名: ${userData.username}`);
    console.log(`   角色: ${userData.role}`);
    console.log(`   邮箱: ${userData.email || '未设置'}`);
    console.log(`   创建时间: ${userData.createdAt}`);

    return userData;
  } catch (error) {
    console.log('❌ 获取用户资料失败');
    if (error.response?.data) {
      console.log(`   错误信息: ${error.response.data.message}`);
    } else {
      console.log(`   错误信息: ${error.message}`);
    }
    return null;
  }
}

/**
 * 主测试函数
 */
async function runPermissionTest() {
  // 1. 管理员登录
  const token = await adminLogin();
  if (!token) {
    console.log('\n💥 无法获取管理员token，测试终止');
    return false;
  }
  
  // 2. 获取用户资料
  const userProfile = await testUserProfile(token);
  if (!userProfile) {
    console.log('\n💥 无法获取用户资料，测试终止');
    return false;
  }
  
  // 3. 测试管理员API
  const apiResults = await testAdminAPIs(token);
  
  // 4. 汇总结果
  console.log('\n📊 测试结果汇总');
  console.log('==================');
  
  const successful = apiResults.filter(r => r.success).length;
  const failed = apiResults.filter(r => !r.success).length;
  
  console.log(`✅ 成功: ${successful}/${apiResults.length}`);
  console.log(`❌ 失败: ${failed}/${apiResults.length}`);
  
  if (failed > 0) {
    console.log('\n🚨 失败的API:');
    apiResults.filter(r => !r.success).forEach(r => {
      console.log(`   - ${r.name}: ${r.status} ${r.error || ''}`);
    });
  }
  
  // 5. 权限分析
  console.log('\n🔍 权限分析:');
  console.log(`   Token有效性: ✅ 有效`);
  console.log(`   用户角色: ${userProfile.role}`);
  console.log(`   管理员权限: ${userProfile.role === 'admin' ? '✅ 有权限' : '❌ 无权限'}`);
  
  // 6. 保存token供其他测试使用
  if (token) {
    try {
      const fs = await import('fs');
      fs.writeFileSync('admin-token.txt', token);
      console.log(`   Token已保存到: admin-token.txt`);
    } catch (error) {
      console.log(`   Token保存失败: ${error.message}`);
    }
  }
  
  const overallSuccess = failed === 0 && userProfile.role === 'admin';
  console.log(`\n🎯 总体状态: ${overallSuccess ? '✅ 正常' : '⚠️ 存在问题'}`);
  
  if (!overallSuccess) {
    console.log('\n🔧 修复建议:');
    if (userProfile.role !== 'admin') {
      console.log('   - 检查用户角色设置，确保测试账号具有管理员权限');
    }
    if (failed > 0) {
      console.log('   - 检查后端API权限验证逻辑');
      console.log('   - 查看后端日志获取详细错误信息');
    }
  }
  
  return overallSuccess;
}

// 运行测试
runPermissionTest()
  .then(success => {
    console.log('\n✅ 权限测试完成！');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 测试过程中发生错误:', error.message);
    process.exit(1);
  });

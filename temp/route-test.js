#!/usr/bin/env node

/**
 * 路由组件测试脚本
 * 验证所有路由引用的组件文件是否存在
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 开始检查路由组件...\n');

// 需要检查的组件路径
const componentPaths = [
  // 公共路由组件
  'src/views/Home.vue',
  'src/views/Login.vue',
  'src/views/FeishuCallback.vue',
  
  // 用户路由组件
  'src/views/user/Profile.vue',
  'src/views/user/Exchanges.vue',
  
  // 管理员路由组件
  'src/views/admin/AdminLayout.vue',
  'src/views/admin/Dashboard.vue',
  'src/views/admin/ProductManagement.vue',
  'src/views/admin/ExchangeManagement.vue',
  'src/views/admin/CategoryManagement.vue',
  'src/views/admin/UserManagement.vue',
  'src/views/admin/FeedbackManagement.vue',
  'src/views/admin/AnnouncementManagement.vue',
  'src/views/admin/LogManagement.vue',
  'src/views/admin/NotificationsPage.vue',
  'src/views/admin/SystemSettings.vue',
  'src/views/admin/HelpCenter.vue',
  
  // 系统管理高级功能组件
  'src/views/admin/system/FeishuMessageTemplates.vue',
  'src/views/admin/system/IntelligentSchedule.vue',
  'src/views/admin/system/DiagnosticTools.vue'
];

let allExists = true;
let missingFiles = [];
let existingFiles = [];

console.log('📁 检查组件文件存在性:\n');

for (const componentPath of componentPaths) {
  const fullPath = path.resolve(componentPath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    console.log(`✅ ${componentPath}`);
    existingFiles.push(componentPath);
  } else {
    console.log(`❌ ${componentPath} - 文件不存在`);
    missingFiles.push(componentPath);
    allExists = false;
  }
}

console.log('\n📊 检查结果统计:');
console.log('==================');
console.log(`总文件数: ${componentPaths.length}`);
console.log(`存在文件: ${existingFiles.length}`);
console.log(`缺失文件: ${missingFiles.length}`);

if (missingFiles.length > 0) {
  console.log('\n❌ 缺失的文件列表:');
  missingFiles.forEach(file => {
    console.log(`   - ${file}`);
  });
  
  console.log('\n🔧 修复建议:');
  console.log('1. 检查文件路径是否正确');
  console.log('2. 确认文件是否被意外删除');
  console.log('3. 检查路由配置中的导入路径');
  console.log('4. 如果文件确实不存在，需要创建对应的Vue组件');
}

console.log(`\n🎯 总体状态: ${allExists ? '✅ 所有组件文件都存在' : '⚠️ 存在缺失文件'}`);

// 额外检查：验证路由配置文件
console.log('\n🔍 检查路由配置文件:');
const routeFiles = [
  'src/router/index.js',
  'src/router/modules/public.js',
  'src/router/modules/user.js',
  'src/router/modules/admin.js',
  'src/router/guards.js'
];

for (const routeFile of routeFiles) {
  const exists = fs.existsSync(routeFile);
  console.log(`${exists ? '✅' : '❌'} ${routeFile}`);
}

console.log('\n✅ 路由组件检查完成！');

process.exit(allExists ? 0 : 1);

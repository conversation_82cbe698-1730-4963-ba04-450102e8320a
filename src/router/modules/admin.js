/**
 * 管理员路由模块
 * 包含所有管理员相关的路由配置
 */

// 管理员页面组件懒加载
const AdminLayout = () => import('../../views/admin/AdminLayout.vue');
const Dashboard = () => import('../../views/admin/Dashboard.vue');
const ProductManagement = () => import('../../views/admin/ProductManagement.vue');
const CategoryManagement = () => import('../../views/admin/CategoryManagement.vue');
const UserManagement = () => import('../../views/admin/UserManagement.vue');
const ExchangeManagement = () => import('../../views/admin/ExchangeManagement.vue');
const FeedbackManagement = () => import('../../views/admin/FeedbackManagement.vue');
const AnnouncementManagement = () => import('../../views/admin/AnnouncementManagement.vue');
const LogManagement = () => import('../../views/admin/LogManagement.vue');
const NotificationsPage = () => import('../../views/admin/NotificationsPage.vue');
const SystemSettings = () => import('../../views/admin/SystemSettings.vue');
const HelpCenter = () => import('../../views/admin/HelpCenter.vue');

// 飞书群管理高级功能
const FeishuMessageTemplates = () => import('../../views/admin/FeishuMessageTemplates.vue');
const IntelligentSchedule = () => import('../../views/admin/IntelligentSchedule.vue');
const DiagnosticTools = () => import('../../views/admin/DiagnosticTools.vue');

// 管理员路由配置
export const adminRoutes = {
  path: '/admin',
  component: AdminLayout,
  meta: { 
    requiresAuth: true, 
    requiresAdmin: true,
    title: '管理后台'
  },
  children: [
    // 数据仪表盘
    {
      path: 'dashboard',
      name: 'AdminDashboard',
      component: Dashboard,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '数据仪表盘',
        icon: 'DataBoard'
      }
    },
    
    // 商品管理
    {
      path: 'products',
      name: 'ProductManagement',
      component: ProductManagement,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '商品管理',
        icon: 'Goods'
      }
    },
    
    // 订单管理
    {
      path: 'exchanges',
      name: 'ExchangeManagement',
      component: ExchangeManagement,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '订单管理',
        icon: 'List'
      }
    },
    
    // 分类管理
    {
      path: 'categories',
      name: 'CategoryManagement',
      component: CategoryManagement,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '分类管理',
        icon: 'Menu'
      }
    },
    
    // 用户管理
    {
      path: 'users',
      name: 'UserManagement',
      component: UserManagement,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '用户管理',
        icon: 'User'
      }
    },
    
    // 反馈管理
    {
      path: 'feedback',
      name: 'FeedbackManagement',
      component: FeedbackManagement,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '反馈管理',
        icon: 'ChatDotRound'
      }
    },
    
    // 公告管理
    {
      path: 'announcements',
      name: 'AnnouncementManagement',
      component: AnnouncementManagement,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '公告管理',
        icon: 'Bell'
      }
    },
    
    // 日志管理
    {
      path: 'logs',
      name: 'LogManagement',
      component: LogManagement,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '日志管理',
        icon: 'Document'
      }
    },
    
    // 通知管理
    {
      path: 'notifications',
      name: 'NotificationsPage',
      component: NotificationsPage,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '通知管理',
        icon: 'Message'
      }
    },
    
    // 系统设置
    {
      path: 'system',
      name: 'SystemSettings',
      component: SystemSettings,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '系统设置',
        icon: 'Setting'
      }
    },
    
    // 帮助中心
    {
      path: 'help',
      name: 'HelpCenter',
      component: HelpCenter,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '帮助中心',
        icon: 'QuestionFilled'
      }
    },
    
    // 飞书群管理高级功能
    {
      path: 'system/message-templates',
      name: 'FeishuMessageTemplates',
      component: FeishuMessageTemplates,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '自定义消息模板',
        icon: 'EditPen'
      }
    },
    
    {
      path: 'system/intelligent-schedule',
      name: 'IntelligentSchedule',
      component: IntelligentSchedule,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '智能发送时间控制',
        icon: 'Timer'
      }
    },
    
    {
      path: 'system/diagnostic-tools',
      name: 'DiagnosticTools',
      component: DiagnosticTools,
      meta: { 
        requiresAuth: true, 
        requiresAdmin: true, 
        title: '高级诊断工具',
        icon: 'Tools'
      }
    },
    
    // 默认重定向到仪表盘
    {
      path: '',
      redirect: '/admin/dashboard'
    }
  ]
};

export default adminRoutes;

/**
 * 用户路由模块
 * 包含所有用户相关的路由配置
 */

// 用户页面组件懒加载
const UserProfile = () => import('../../views/user/Profile.vue');
const UserExchanges = () => import('../../views/user/Exchanges.vue');

// 用户路由配置
export const userRoutes = {
  path: '/user',
  meta: { 
    requiresAuth: true,
    title: '用户中心'
  },
  children: [
    // 个人中心
    {
      path: 'profile',
      name: 'UserProfile',
      component: UserProfile,
      meta: { 
        requiresAuth: true, 
        title: '个人中心',
        icon: 'User'
      }
    },
    
    // 我的兑换
    {
      path: 'exchanges',
      name: 'UserExchanges',
      component: UserExchanges,
      meta: { 
        requiresAuth: true, 
        title: '我的兑换',
        icon: 'List'
      }
    },
    
    // 默认重定向到个人中心
    {
      path: '',
      redirect: '/user/profile'
    }
  ]
};

export default userRoutes;

/**
 * 公共路由模块
 * 包含所有公开访问的路由配置
 */

// 公共页面组件懒加载
const Home = () => import('../../views/Home.vue');
const Login = () => import('../../views/Login.vue');
const FeishuCallback = () => import('../../views/FeishuCallback.vue');

// 公共路由配置
export const publicRoutes = [
  // 首页
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { 
      title: '商品展示',
      icon: 'House'
    }
  },
  
  // 登录页
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { 
      title: '登录', 
      guest: true,
      icon: 'Key'
    }
  },
  
  // 飞书登录回调路由
  {
    path: '/feishu/callback',
    name: 'FeishuCallback',
    component: FeishuCallback,
    meta: { 
      title: '飞书登录处理', 
      guest: true,
      hidden: true // 在导航中隐藏
    }
  }
];

export default publicRoutes;

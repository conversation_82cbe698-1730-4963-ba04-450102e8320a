import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '../stores/auth';

// 导入路由模块
import { publicRoutes } from './modules/public';
import { userRoutes } from './modules/user';
import { adminRoutes } from './modules/admin';

// 路由守卫配置
import { setupRouterGuards } from './guards';

// 合并所有路由
const routes = [
  // 公共路由
  ...publicRoutes,
  
  // 用户路由
  userRoutes,
  
  // 管理员路由
  adminRoutes,
  
  // 404重定向
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/',
    meta: { hidden: true }
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  // 路由滚动行为
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 设置路由守卫
setupRouterGuards(router);

export default router;

/**
 * 路由守卫配置
 * 统一管理路由权限验证和页面标题设置
 */

import { useAuthStore } from '../stores/auth';
import { ElMessage } from 'element-plus';

/**
 * 设置页面标题
 * @param {Object} to - 目标路由
 */
function setPageTitle(to) {
  const baseTitle = '光年小卖部';
  const pageTitle = to.meta.title;
  
  if (pageTitle) {
    document.title = `${pageTitle} - ${baseTitle}`;
  } else {
    document.title = baseTitle;
  }
}

/**
 * 检查认证状态
 * @param {Object} authStore - 认证store
 * @returns {boolean} - 是否已认证
 */
async function checkAuthentication(authStore) {
  const isAuthenticated = authStore.isAuthenticated;
  
  // 如果用户已登录但没有用户信息，尝试获取用户资料
  if (isAuthenticated && !authStore.user) {
    try {
      await authStore.getProfile();
      return true;
    } catch (error) {
      console.error('获取用户资料失败:', error);
      // 如果获取用户资料失败，清除认证状态
      authStore.logout();
      ElMessage.error('登录状态已过期，请重新登录');
      return false;
    }
  }
  
  return isAuthenticated;
}

/**
 * 检查管理员权限
 * @param {Object} authStore - 认证store
 * @returns {boolean} - 是否为管理员
 */
function checkAdminPermission(authStore) {
  return authStore.isAdmin;
}

/**
 * 处理认证失败
 * @param {Object} to - 目标路由
 * @param {Function} next - 路由跳转函数
 */
function handleAuthFailure(to, next) {
  console.warn('用户未登录，跳转到登录页');
  next({ 
    name: 'Login', 
    query: { redirect: to.fullPath } 
  });
}

/**
 * 处理权限不足
 * @param {Object} to - 目标路由
 * @param {Function} next - 路由跳转函数
 */
function handlePermissionDenied(to, next) {
  console.warn('权限不足，跳转到首页:', to.path);
  ElMessage.warning('您没有权限访问该页面');
  next({ name: 'Home' });
}

/**
 * 全局前置守卫
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由跳转函数
 */
async function beforeEachGuard(to, from, next) {
  // 设置页面标题
  setPageTitle(to);
  
  // 获取认证store
  const authStore = useAuthStore();
  
  try {
    // 检查认证状态
    const isAuthenticated = await checkAuthentication(authStore);
    
    // 检查是否需要登录
    if (to.meta.requiresAuth) {
      if (!isAuthenticated) {
        return handleAuthFailure(to, next);
      }
    }
    
    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin) {
      if (!isAuthenticated) {
        return handleAuthFailure(to, next);
      } else if (!checkAdminPermission(authStore)) {
        return handlePermissionDenied(to, next);
      }
    }
    
    // 访客页面（如登录页）已登录用户不应访问
    if (to.meta.guest && isAuthenticated) {
      console.log('已登录用户访问访客页面，跳转到首页');
      return next({ name: 'Home' });
    }
    
    // 通过所有检查，允许访问
    next();
    
  } catch (error) {
    console.error('路由守卫执行失败:', error);
    ElMessage.error('页面加载失败，请稍后重试');
    next({ name: 'Home' });
  }
}

/**
 * 全局后置守卫
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 */
function afterEachGuard(to, from) {
  // 页面加载完成后的处理
  console.log(`路由跳转完成: ${from.path} -> ${to.path}`);
  
  // 可以在这里添加页面访问统计等逻辑
  if (to.meta.title) {
    console.log(`页面标题: ${to.meta.title}`);
  }
}

/**
 * 路由错误处理
 * @param {Error} error - 路由错误
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 */
function onError(error, to, from) {
  console.error('路由错误:', error);
  console.error(`错误发生在路由跳转: ${from.path} -> ${to.path}`);
  
  ElMessage.error('页面加载失败，请刷新页面重试');
}

/**
 * 设置路由守卫
 * @param {Object} router - 路由实例
 */
export function setupRouterGuards(router) {
  // 全局前置守卫
  router.beforeEach(beforeEachGuard);
  
  // 全局后置守卫
  router.afterEach(afterEachGuard);
  
  // 路由错误处理
  router.onError(onError);
  
  console.log('✅ 路由守卫设置完成');
}

export default {
  setupRouterGuards,
  beforeEachGuard,
  afterEachGuard,
  onError
};

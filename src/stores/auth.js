import { defineStore } from 'pinia';
import { login as loginApi, getProfile as getProfile<PERSON>pi, feishuLogin as feishuLogin<PERSON>pi, refreshToken as refreshTokenApi } from '../api/auth';
import api from '../api';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    // 用户基本信息
    user: null,
    // JWT令牌
    token: localStorage.getItem('token') || sessionStorage.getItem('token') || null,
    // 是否登录中
    loading: false,
    // 错误信息
    error: null,
    // 是否已初始化
    initialized: false,
    // 飞书登录相关状态
    feishuLoginState: sessionStorage.getItem('feishu_state') || null
  }),

  getters: {
    // 是否已认证
    isAuthenticated: (state) => !!state.token && !!state.user,
    
    // 是否为管理员
    isAdmin: (state) => {
      // 安全地检查用户是否存在且角色是否为admin
      if (!state.user) return false;
      
      // 主角色检查
      const isAdminRole = state.user.role === 'admin';
      
      // 权限属性检查（兼容性检查）
      const hasAdminPerm = state.user.permissions?.includes('admin') || false;
      
      const result = isAdminRole || hasAdminPerm;
      
      // 记录日志以便调试
      if (import.meta.env.DEV) {
        console.log('管理员状态计算:', { 
          user: state.user?.id,
          role: state.user?.role, 
          permissions: state.user?.permissions,
          isAdminRole,
          hasAdminPerm,
          result
        });
      }
      
      return result;
    },
    
    // 用户头像
    avatar: (state) => state.user?.avatar || null
  },

  actions: {
    // 初始化认证状态
    async initialize() {
      if (this.initialized) return;

      // 添加初始化标识，避免重复初始化
      let initAttempt = 0;
      const maxRetries = 2; // 最多重试2次

      if (this.token) {
        try {
          console.log('检测到token，正在获取用户资料...');
          
          const attemptProfile = async () => {
            try {
              await this.getProfile();
              console.log('用户资料获取成功，用户角色:', this.user?.role);
              
              // 额外验证用户信息完整性
              if (!this.user || !this.user.id) {
                console.warn('用户资料不完整，重置认证状态');
                this.logout();
                return false;
              }
              
              return true;
            } catch (profileError) {
              initAttempt++;
              console.error(`初始化获取用户资料失败(尝试${initAttempt}/${maxRetries}):`, profileError);
              
              // 如果是网络错误并且未超过最大重试次数
              if (!profileError.response && initAttempt < maxRetries) {
                console.log(`500ms后重试获取用户资料(${initAttempt}/${maxRetries})...`);
                await new Promise(resolve => setTimeout(resolve, 500));
                return attemptProfile();
              }
              
              // 超过重试次数或非网络错误
              return false;
            }
          };
          
          const profileSuccess = await attemptProfile();
          
          if (!profileSuccess) {
            console.warn('多次尝试获取用户资料失败，清除认证状态');
            this.logout();
          }
        } catch (error) {
          console.error('初始化过程中发生错误:', error);
          // 如果获取用户资料失败，清除无效token
          this.logout();
        }
      }

      this.initialized = true;
      console.log('认证状态初始化完成:', {
        isAuthenticated: this.isAuthenticated,
        isAdmin: this.isAdmin,
        userId: this.user?.id,
        userRole: this.user?.role
      });
    },

    async login(credentials) {
      this.loading = true;
      this.error = null;
      
      try {
        console.log('正在调用登录API:', credentials.username);
        const response = await loginApi(credentials);
        console.log('登录API返回成功:', response);
        
        this.user = response.user;
        this.token = response.token;
        
        // 无论是否记住我，都只将token存储在sessionStorage中
        // 这样确保关闭浏览器后重新打开网页时为未登录状态
        sessionStorage.setItem('token', response.token);
        // 确保清除localStorage中可能存在的token
        localStorage.removeItem('token');
        
        return response;
      } catch (error) {
        console.error('登录API错误:', error);
        
        // 更详细的错误处理
        if (error.message) {
          this.error = error.message;
        } else if (error.response && error.response.data && error.response.data.message) {
          this.error = error.response.data.message;
        } else {
          this.error = '登录失败，服务器未返回错误信息';
        }
        
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async getProfile() {
      if (!this.token) return null;
      
      this.loading = true;
      this.error = null;
      
      try {
        const response = await getProfileApi();
        this.user = response.user;
        return response.user;
      } catch (error) {
        this.error = error.message || '获取用户资料失败';
        // 如果是401错误，清除用户信息和token
        if (error.status === 401) {
          this.logout();
        }
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async feishuLogin(code) {
      this.loading = true;
      this.error = null;
      
      try {
        console.log('正在调用飞书登录API，授权码长度:', code.length);
        const response = await feishuLoginApi({ code });
        console.log('飞书登录API返回成功:', response);
        
        this.user = response.user;
        this.token = response.token;
        
        // 存储令牌（飞书登录默认使用长期令牌）
        sessionStorage.setItem('token', response.token);
        
        return response;
      } catch (error) {
        console.error('飞书登录API错误:', error);
        
        // 更详细的错误处理
        if (error.message) {
          this.error = error.message;
        } else if (error.response && error.response.data && error.response.data.message) {
          this.error = error.response.data.message;
        } else {
          this.error = '飞书登录失败，服务器未返回错误信息';
        }
        
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async logout() {
      // Call the backend logout endpoint first
      try {
        // Check if the token exists before calling the API
        if (this.token) {
          console.log('Calling backend logout API...');
          await api.post('/auth/logout'); 
          console.log('Backend logout successful.');
        }
      } catch (error) {
        // Log the error but proceed with local cleanup
        console.error('Backend logout failed:', error);
        // Optionally notify the user, but don't block logout
        // ElMessage.error('退出请求失败，但本地已登出');
      }
      
      // Always perform local cleanup regardless of API call success
      console.log('Performing local logout cleanup...');
      this.user = null;
      this.token = null;
      
      // 清除所有相关的存储
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      
      // 清除记住我相关的信息
      localStorage.removeItem('remember_login');
      localStorage.removeItem('remember_username');
      localStorage.removeItem('remember_email');
      localStorage.removeItem('remember_password');
      
      // 清除飞书相关信息
      sessionStorage.removeItem('feishu_state');
      
      console.log('Local logout cleanup complete.');
    },
    
    // 刷新用户令牌
    async refreshToken() {
      if (!this.token) return null;
      
      this.loading = true;
      this.error = null;
      
      try {
        console.log('正在刷新令牌...');
        const response = await refreshTokenApi();
        
        // 更新用户信息和令牌
        this.user = response.user;
        this.token = response.token;
        
        // 保存到会话存储
        sessionStorage.setItem('token', response.token);
        
        console.log('令牌刷新成功，用户角色:', this.user.role);
        return response;
      } catch (error) {
        console.error('刷新令牌错误:', error);
        this.error = error.message || '刷新令牌失败';
        
        // 如果是401错误，清除用户信息和token
        if (error.response && error.response.status === 401) {
          this.logout();
        }
        
        throw error;
      } finally {
        this.loading = false;
      }
    }
  }
}); 
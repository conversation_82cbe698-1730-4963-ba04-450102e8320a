import axios from 'axios';
import { useAuthStore } from '../stores/auth';
import performanceMonitor from '../utils/performance';

// 简单的内存缓存
class ApiCache {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5分钟TTL
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  set(key, data, customTtl) {
    const ttl = customTtl || this.ttl;
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
  }

  clear() {
    this.cache.clear();
  }

  delete(pattern) {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }
}

const apiCache = new ApiCache();

// 生成缓存键
function generateCacheKey(config) {
  const { method, url, params } = config;
  const key = `${method}:${url}`;

  if (params) {
    const sortedParams = Object.keys(params).sort().map(k => `${k}=${params[k]}`).join('&');
    return `${key}?${sortedParams}`;
  }

  return key;
}

// 判断请求是否可缓存
function isCacheable(config) {
  const method = config.method.toLowerCase();
  const url = config.url;

  // 只缓存GET请求
  if (method !== 'get') return false;

  // 不缓存的路径
  const noCachePaths = [
    '/auth/profile',
    '/logs',
    '/exports',
    '/stats',
    '/dashboard'
  ];

  return !noCachePaths.some(path => url.includes(path));
}

// 创建一个axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    const authStore = useAuthStore();
    const startTime = Date.now();

    // 记录请求开始时间
    config.metadata = { startTime };

    // 检查缓存
    if (isCacheable(config)) {
      const cacheKey = generateCacheKey(config);
      const cachedResponse = apiCache.get(cacheKey);

      if (cachedResponse) {
        if (import.meta.env.DEV) {
          console.log(`📦 使用缓存: ${config.url}`);
        }

        // 创建模拟的响应对象
        const mockResponse = {
          data: cachedResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config,
          fromCache: true
        };

        // 直接返回Promise.resolve，跳过实际请求
        return Promise.resolve(mockResponse);
      }

      config.cacheKey = cacheKey;
    }

    // 需要认证的API路径列表
    const requireAuthPaths = [
      '/notifications',
      '/auth/profile',
      '/auth/logout',
      '/users',
      '/admin',
      '/products/upload',
      '/feedback',
      '/message-templates',
      '/system'
    ];

    // 检查是否需要认证
    const needsAuth = requireAuthPaths.some(path => config.url.startsWith(path));

    if (authStore.token) {
      config.headers['Authorization'] = `Bearer ${authStore.token}`;
    } else if (needsAuth) {
      return Promise.reject({
        message: '用户未登录，请先登录',
        status: 401,
        config
      });
    }

    // 对于文件上传请求，确保不设置Content-Type
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { config } = response;
    const duration = Date.now() - (config.metadata?.startTime || 0);

    // 记录性能指标
    performanceMonitor.recordApiRequest(
      config.url,
      config.method.toUpperCase(),
      duration,
      response.status
    );

    // 缓存响应（如果适用）
    if (config.cacheKey && !response.fromCache) {
      apiCache.set(config.cacheKey, response.data);
    }

    // 开发环境日志
    if (import.meta.env.DEV) {
      console.log(`API响应成功: ${config.method.toUpperCase()} ${config.url} (${duration}ms)`);
    }

    return response.data;
  },
  error => {
    const { config } = error;
    const duration = Date.now() - (config?.metadata?.startTime || 0);

    // 记录错误的性能指标
    if (config) {
      performanceMonitor.recordApiRequest(
        config.url,
        config.method.toUpperCase(),
        duration,
        error.response?.status || 0
      );
    }

    // 网络错误
    if (!error.response) {
      console.error('网络错误，无法连接到服务器');
      return Promise.reject({
        message: '网络错误，无法连接到服务器',
        status: 0
      });
    }

    // 处理401错误 (未授权)
    if (error.response.status === 401) {
      const authStore = useAuthStore();
      if (authStore.token) {
        console.warn('Token已过期，正在登出...');
        authStore.logout();
        // 清除相关缓存
        apiCache.delete('/auth');
        apiCache.delete('/users');
      }
    }

    // 开发环境错误日志
    if (import.meta.env.DEV) {
      console.error(`API响应错误: ${error.response.status} ${error.response.statusText}`);
      console.error('请求URL:', config?.url);
    }

    return Promise.reject(error.response?.data || {
      message: error.message || '请求失败',
      status: error.response?.status || 500
    });
  }
);

// 导出缓存控制方法
export const cacheControl = {
  clear: () => apiCache.clear(),
  delete: (pattern) => apiCache.delete(pattern),
  get: (key) => apiCache.get(key),
  set: (key, data, ttl) => apiCache.set(key, data, ttl)
};

export default api;
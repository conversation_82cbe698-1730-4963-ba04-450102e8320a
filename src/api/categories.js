import api from './index';
import { cacheControl } from './index';

/**
 * 获取所有分类
 * @param {boolean} [forceRefresh=false] - 是否强制刷新缓存
 * @returns {Promise} - 返回分类列表
 */
export const getCategories = async (forceRefresh = false) => {
  // 强制刷新时清除缓存
  if (forceRefresh) {
    cacheControl.delete('/categories');
  }
  
  try {
    return await api.get('/categories');
  } catch (error) {
    console.error('获取分类失败:', error);
    
    // 如果是网络错误，提供默认空分类数组
    if (!error.response) {
      console.warn('获取分类网络错误，返回默认空分类列表');
      return [];
    }
    
    // 如果发生错误，尝试再次请求（不使用缓存）
    if (!error._retry) {
      error._retry = true;
      console.log('正在重新请求分类数据...');
      // 清除可能存在的错误缓存
      cacheControl.delete('/categories');
      // 延迟300ms后重试
      await new Promise(resolve => setTimeout(resolve, 300));
      try {
        return await api.get('/categories');
      } catch (retryError) {
        console.error('重试获取分类失败:', retryError);
        return []; // 重试失败也返回空数组
      }
    }
    
    return []; // 确保总是返回有效值，避免UI崩溃
  }
};

/**
 * 获取分类及其商品数量
 * @param {boolean} [forceRefresh=false] - 是否强制刷新缓存
 * @returns {Promise} - 返回分类列表及每个分类下的商品数量
 */
export const getCategoriesWithProductCount = async (forceRefresh = false) => {
  // 强制刷新时清除缓存
  if (forceRefresh) {
    cacheControl.delete('/categories/with-product-count');
  }
  
  try {
    return await api.get('/categories/with-product-count');
  } catch (error) {
    console.error('获取分类及商品数量失败:', error);
    
    // 如果是网络错误，提供默认空分类数组
    if (!error.response) {
      console.warn('获取分类及商品数量网络错误，返回默认空列表');
      return [];
    }
    
    // 如果发生错误，尝试再次请求（不使用缓存）
    if (!error._retry) {
      error._retry = true;
      console.log('正在重新请求分类及商品数量数据...');
      // 清除可能存在的错误缓存
      cacheControl.delete('/categories/with-product-count');
      // 延迟300ms后重试
      await new Promise(resolve => setTimeout(resolve, 300));
      try {
        return await api.get('/categories/with-product-count');
      } catch (retryError) {
        console.error('重试获取分类及商品数量失败:', retryError);
        return []; // 重试失败也返回空数组
      }
    }
    
    return []; // 确保总是返回有效值，避免UI崩溃
  }
};

/**
 * 获取单个分类详情
 * @param {number} categoryId - 分类ID
 * @returns {Promise} - 返回分类详情
 */
export const getCategoryById = async (categoryId) => {
  try {
    return await api.get(`/categories/${categoryId}`);
  } catch (error) {
    console.error(`获取分类(ID:${categoryId})详情失败:`, error);
    // 如果是网络错误，提供默认空对象
    if (!error.response) {
      console.warn(`获取分类(ID:${categoryId})详情网络错误，返回默认空对象`);
      return { id: categoryId, name: '未知分类', description: '', sortOrder: 0 };
    }
    throw error;
  }
};

/**
 * 创建新分类
 * @param {Object} categoryData - 分类数据，包含name、description、sortOrder
 * @returns {Promise} - 返回创建的分类
 */
export const createCategory = (categoryData) => {
  return api.post('/categories', categoryData);
};

/**
 * 更新分类
 * @param {number} categoryId - 分类ID
 * @param {Object} categoryData - 更新的分类数据
 * @returns {Promise} - 返回更新后的分类
 */
export const updateCategory = (categoryId, categoryData) => {
  return api.put(`/categories/${categoryId}`, categoryData);
};

/**
 * 删除分类
 * @param {number} categoryId - 分类ID
 * @returns {Promise} - 返回操作结果
 */
export const deleteCategory = (categoryId) => {
  return api.delete(`/categories/${categoryId}`);
}; 
import api from './index';
import { cacheControl } from './index';

/**
 * 获取当前用户的通知列表
 * @param {boolean} [forceRefresh=false] - 是否强制刷新缓存
 * @returns {Promise<Object>} 返回通知列表
 */
export async function getNotifications(forceRefresh = false) {
  // 强制刷新时清除缓存
  if (forceRefresh) {
    cacheControl.delete('/notifications');
  }
  
  try {
    return await api.get('/notifications');
  } catch (error) {
    console.error('获取通知列表失败:', error);
    // 如果是网络错误，提供默认空数据，防止UI崩溃
    if (!error.response) {
      console.warn('获取通知列表网络错误，返回默认空列表');
      return { data: [], total: 0 };
    }
    throw error;
  }
}

/**
 * 获取未读通知数量
 * @param {boolean} [forceRefresh=false] - 是否强制刷新缓存
 * @returns {Promise<Object>} 返回未读数量
 */
export async function getUnreadCount(forceRefresh = false) {
  // 强制刷新时清除缓存
  if (forceRefresh) {
    cacheControl.delete('/notifications/unread-count');
  }
  
  try {
    return await api.get('/notifications/unread-count');
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    // 网络错误或请求被拒绝时返回默认值0，避免UI崩溃
    console.warn('获取未读通知数量失败，返回默认值0');
    return { count: 0, data: { count: 0 } };
  }
}

/**
 * 标记通知为已读
 * @param {number} id - 通知ID
 * @returns {Promise<Object>} 返回操作结果
 */
export async function markAsRead(id) {
  try {
    const response = await api.put(`/notifications/${id}/read`);
    // 操作成功后清除缓存
    cacheControl.delete('/notifications');
    cacheControl.delete('/notifications/unread-count');
    return response;
  } catch (error) {
    console.error(`标记通知(${id})为已读失败:`, error);
    // 网络错误时返回模拟成功响应
    if (!error.response) {
      console.warn(`标记通知(${id})为已读网络错误，返回模拟成功响应`);
      return { success: true };
    }
    throw error;
  }
}

/**
 * 标记所有通知为已读
 * @returns {Promise<Object>} 返回操作结果
 */
export async function markAllAsRead() {
  try {
    const response = await api.put('/notifications/read-all');
    // 操作成功后清除缓存
    cacheControl.delete('/notifications');
    cacheControl.delete('/notifications/unread-count');
    return response;
  } catch (error) {
    console.error('标记所有通知为已读失败:', error);
    // 网络错误时返回模拟成功响应
    if (!error.response) {
      console.warn('标记所有通知为已读网络错误，返回模拟成功响应');
      return { success: true };
    }
    throw error;
  }
}

/**
 * 删除通知
 * @param {number} id - 通知ID
 * @returns {Promise<Object>} 返回操作结果
 */
export async function deleteNotification(id) {
  try {
    const response = await api.delete(`/notifications/${id}`);
    // 操作成功后清除缓存
    cacheControl.delete('/notifications');
    return response;
  } catch (error) {
    console.error(`删除通知(${id})失败:`, error);
    // 网络错误时返回模拟成功响应
    if (!error.response) {
      console.warn(`删除通知(${id})网络错误，返回模拟成功响应`);
      return { success: true };
    }
    throw error;
  }
} 
import api from './index';
import { cacheControl } from './index';

/**
 * 提交用户反馈
 * @param {Object} feedbackData - 反馈数据
 * @param {string} feedbackData.type - 反馈类型 ('product', 'feature', 'bug', 'other')
 * @param {string} feedbackData.title - 反馈标题
 * @param {string} feedbackData.content - 反馈内容
 * @returns {Promise<Object>} - 响应对象
 */
export function submitFeedback(feedbackData) {
  return api.post('/feedback', feedbackData);
}

/**
 * 获取当前用户的反馈列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {boolean} [forceRefresh=false] - 是否强制刷新缓存
 * @returns {Promise<Object>} - 响应对象，包含反馈列表和分页信息
 */
export async function getUserFeedbacks(params, forceRefresh = false) {
  // 强制刷新时清除缓存
  if (forceRefresh) {
    cacheControl.delete('/feedback/user');
  }
  
  try {
    return await api.get('/feedback/user', { params });
  } catch (error) {
    console.error('获取用户反馈列表失败:', error);
    // 如果发生网络错误，尝试再次请求（不使用缓存）
    if (!error.response && !error._retry) {
      error._retry = true;
      console.log('尝试重新获取用户反馈列表...');
      cacheControl.delete('/feedback/user');
      return api.get('/feedback/user', { params });
    }
    throw error;
  }
}

/**
 * 获取反馈详情
 * @param {string} id - 反馈ID
 * @returns {Promise<Object>} - 响应对象，包含反馈详情
 */
export async function getFeedbackDetail(id) {
  try {
    return await api.get(`/feedback/${id}`);
  } catch (error) {
    console.error(`获取反馈详情(ID:${id})失败:`, error);
    // 如果发生网络错误，尝试再次请求
    if (!error.response && !error._retry) {
      error._retry = true;
      console.log(`尝试重新获取反馈详情(ID:${id})...`);
      return api.get(`/feedback/${id}`);
    }
    throw error;
  }
}

/**
 * 获取所有反馈列表 (仅管理员可用)
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 状态过滤 ('pending', 'processing', 'completed')
 * @param {string} params.type - 类型过滤 ('product', 'feature', 'bug', 'other')
 * @param {boolean} [forceRefresh=false] - 是否强制刷新缓存
 * @returns {Promise<Object>} - 响应对象，包含反馈列表和分页信息
 */
export async function getAllFeedbacks(params, forceRefresh = false) {
  const cacheKey = `/feedback?${new URLSearchParams(params).toString()}`;
  
  // 强制刷新时清除缓存
  if (forceRefresh) {
    cacheControl.delete('/feedback');
  }
  
  try {
    return await api.get('/feedback', { params });
  } catch (error) {
    console.error('获取所有反馈列表失败:', error);
    // 如果发生网络错误，尝试再次请求（不使用缓存）
    if (!error.response && !error._retry) {
      error._retry = true;
      console.log('尝试重新获取所有反馈列表...');
      cacheControl.delete('/feedback');
      // 延迟300ms后重试
      await new Promise(resolve => setTimeout(resolve, 300));
      return api.get('/feedback', { params });
    }
    throw error;
  }
}

/**
 * 更新反馈状态和回复 (仅管理员可用)
 * @param {string} id - 反馈ID
 * @param {Object} updateData - 更新数据
 * @param {string} updateData.status - 状态 ('pending', 'processing', 'completed')
 * @param {string} updateData.adminReply - 管理员回复
 * @returns {Promise<Object>} - 响应对象
 */
export async function updateFeedback(id, updateData) {
  try {
    const response = await api.put(`/feedback/${id}`, updateData);
    // 更新成功后，清除相关缓存
    cacheControl.delete('/feedback');
    return response;
  } catch (error) {
    console.error(`更新反馈(ID:${id})失败:`, error);
    // 如果发生网络错误，尝试再次请求
    if (!error.response && !error._retry) {
      error._retry = true;
      console.log(`尝试重新更新反馈(ID:${id})...`);
      return api.put(`/feedback/${id}`, updateData);
    }
    throw error;
  }
} 
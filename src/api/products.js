import api from './index';

/**
 * 获取商品列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.limit - 每页数量，默认10
 * @param {string} params.category - 单个分类ID
 * @param {string} params.categories - 多个分类ID，逗号分隔
 * @param {string} params.search - 搜索关键词
 * @param {string} params.sort - 排序方式
 *   - default: 默认排序（新品和热门优先，再按光年币价格升序）
 *   - ly-asc: 光年币价格升序
 *   - ly-desc: 光年币价格降序
 *   - rmb-asc: 人民币价格升序
 *   - rmb-desc: 人民币价格降序
 *   - newest: 最新添加
 *   - oldest: 最早添加
 *   - name-asc: 名称升序
 *   - name-desc: 名称降序
 *   - stock-asc: 库存升序
 *   - stock-desc: 库存降序
 * @param {number} params.minLyPrice - 最低光年币价格
 * @param {number} params.maxLyPrice - 最高光年币价格
 * @param {number} params.minRmbPrice - 最低人民币价格
 * @param {number} params.maxRmbPrice - 最高人民币价格
 * @param {boolean} params.inStock - 仅显示有库存商品
 * @param {boolean} params.isNew - 仅显示新品
 * @param {boolean} params.isHot - 仅显示热门商品
 * @param {string} params.startDate - 开始日期 YYYY-MM-DD
 * @param {string} params.endDate - 结束日期 YYYY-MM-DD
 * @param {string} params.status - 商品状态 (active/inactive)
 * @param {boolean} params.showAll - 是否显示所有商品（包括下架商品）
 * @returns {Promise} - 返回商品列表和分页信息
 */
export const getProducts = (params = {}) => {
  return api.get('/products', { params });
};

/**
 * 获取商品价格范围
 * 用于动态设置价格筛选滑块的最小值和最大值
 * @param {Object} params - 查询参数
 * @param {string} params.category - 分类ID，可选筛选条件
 * @param {string} params.search - 搜索关键词，可选筛选条件
 * @param {boolean} params.showAll - 是否包含下架商品
 * @returns {Promise} - 返回价格范围 {minLyPrice, maxLyPrice, minRmbPrice, maxRmbPrice}
 */
export const getProductPriceRanges = (params = {}) => {
  return api.get('/products/price-ranges', { params });
};

/**
 * 获取商品详情
 * @param {number} id - 商品ID
 * @returns {Promise} - 返回商品详情
 */
export const getProductById = (id) => {
  return api.get(`/products/${id}`);
};

/**
 * 获取商品统计信息
 * @returns {Promise} - 返回商品统计数据
 */
export const getProductStats = () => {
  console.log('调用getProductStats API');
  return api.get('/products/stats')
    .then(response => {
      console.log('商品统计API响应:', response);
      return response;
    })
    .catch(error => {
      console.error('获取商品统计失败:', error);
      throw error;
    });
};

/**
 * 获取最新商品
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 返回数量限制，默认5条
 * @returns {Promise} - 返回最新商品列表，按创建时间排序
 */
export const getRecentProducts = (params = {}) => {
  console.log('调用getRecentProducts API，参数:', params);
  return api.get('/products/recent', { params })
    .then(response => {
      console.log('最新商品API响应:', response);
      return response;
    })
    .catch(error => {
      console.error('获取最新商品失败:', error);
      throw error;
    });
};

/**
 * 获取热门商品
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 返回数量限制，默认5条
 * @returns {Promise} - 返回热门商品列表，按兑换数量排序
 */
export const getPopularProducts = (params = {}) => {
  console.log('调用getPopularProducts API，参数:', params);
  return api.get('/products/popular', { params })
    .then(response => {
      console.log('热门商品API响应:', response);
      return response;
    })
    .catch(error => {
      console.error('热门商品API错误:', error);
      throw error;
    });
};

/**
 * 添加商品(管理员)
 * @param {Object} productData - 商品数据
 * @returns {Promise} - 返回添加的商品
 */
export const createProduct = (productData) => {
  return api.post('/products', productData);
};

/**
 * 更新商品(管理员)
 * @param {number} id - 商品ID
 * @param {Object} productData - 更新的商品数据
 * @returns {Promise} - 返回更新后的商品
 */
export const updateProduct = (id, productData) => {
  // 确保日期字段以ISO格式传递
  const data = { ...productData };
  
  // 如果有上线日期字段，确保正确处理
  if (data.createdAt) {
    // 如果是字符串且不是ISO格式，转换为Date对象再转回标准ISO字符串
    if (typeof data.createdAt === 'string' && !data.createdAt.endsWith('Z')) {
      data.createdAt = new Date(data.createdAt).toISOString();
    }
  }
  
  return api.put(`/products/${id}`, data);
};

/**
 * 删除商品(管理员)
 * @param {number} id - 商品ID
 * @returns {Promise} - 返回操作结果
 */
export const deleteProduct = (id) => {
  return api.delete(`/products/${id}`);
};

/**
 * 批量删除商品(管理员)
 * @param {Array} ids - 商品ID数组
 * @returns {Promise} - 返回操作结果
 */
export const bulkDeleteProducts = (ids) => {
  return api.post('/products/bulk-delete', { ids });
};

/**
 * 更新商品状态(管理员)
 * @param {number} id - 商品ID
 * @param {string} status - 商品状态 ('active'/'inactive')
 * @returns {Promise} - 返回操作结果
 */
export const updateProductStatus = (id, status) => {
  return api.put(`/products/${id}/status`, { status });
};

/**
 * 导出商品数据(管理员)
 * @param {Object} params - 导出参数，与查询参数相同
 * @returns {Promise} - 返回可下载的文件URL
 */
export const exportProducts = (params = {}) => {
  return api.get('/products/export', { 
    params,
    responseType: 'blob' 
  });
};

/**
 * 批量导入商品数据(管理员)
 * @param {FormData} formData - 包含商品数据的CSV/Excel文件
 * @returns {Promise} - 返回导入结果
 */
export const importProducts = (formData) => {
  return api.post('/products/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}; 
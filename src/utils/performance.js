/**
 * 性能监控工具
 * 用于监控页面加载性能、API请求性能等
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
    this.init();
  }

  /**
   * 初始化性能监控
   */
  init() {
    if (typeof window === 'undefined') return;
    
    // 监控页面加载性能
    this.observePageLoad();
    
    // 监控资源加载性能
    this.observeResourceLoad();
    
    // 监控长任务
    this.observeLongTasks();
    
    // 监控内存使用
    this.observeMemoryUsage();
  }

  /**
   * 监控页面加载性能
   */
  observePageLoad() {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0];
          if (navigation) {
            this.recordMetric('page-load', {
              dns: navigation.domainLookupEnd - navigation.domainLookupStart,
              tcp: navigation.connectEnd - navigation.connectStart,
              request: navigation.responseStart - navigation.requestStart,
              response: navigation.responseEnd - navigation.responseStart,
              dom: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
              load: navigation.loadEventEnd - navigation.loadEventStart,
              total: navigation.loadEventEnd - navigation.navigationStart
            });
          }
        }, 0);
      });
    }
  }

  /**
   * 监控资源加载性能
   */
  observeResourceLoad() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.recordMetric('resource-load', {
              name: entry.name,
              duration: entry.duration,
              size: entry.transferSize || 0,
              type: this.getResourceType(entry.name)
            });
          }
        });
      });
      
      observer.observe({ entryTypes: ['resource'] });
      this.observers.push(observer);
    }
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'longtask') {
              this.recordMetric('long-task', {
                duration: entry.duration,
                startTime: entry.startTime
              });
              
              // 开发环境警告
              if (import.meta.env.DEV) {
                console.warn(`⚠️ 长任务检测: ${entry.duration}ms`);
              }
            }
          });
        });
        
        observer.observe({ entryTypes: ['longtask'] });
        this.observers.push(observer);
      } catch (e) {
        // longtask API可能不被支持
        console.warn('Long task monitoring not supported');
      }
    }
  }

  /**
   * 监控内存使用
   */
  observeMemoryUsage() {
    if ('performance' in window && 'memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        this.recordMetric('memory-usage', {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          timestamp: Date.now()
        });
      }, 30000); // 每30秒记录一次
    }
  }

  /**
   * 记录API请求性能
   */
  recordApiRequest(url, method, duration, status) {
    this.recordMetric('api-request', {
      url,
      method,
      duration,
      status,
      timestamp: Date.now()
    });
    
    // 性能警告
    if (import.meta.env.DEV && duration > 3000) {
      console.warn(`🐌 慢API请求: ${url} (${duration}ms)`);
    }
  }

  /**
   * 记录路由切换性能
   */
  recordRouteChange(from, to, duration) {
    this.recordMetric('route-change', {
      from: from.path,
      to: to.path,
      duration,
      timestamp: Date.now()
    });
    
    // 性能警告
    if (import.meta.env.DEV && duration > 1000) {
      console.warn(`🐌 慢路由切换: ${from.path} -> ${to.path} (${duration}ms)`);
    }
  }

  /**
   * 记录组件渲染性能
   */
  recordComponentRender(componentName, duration) {
    this.recordMetric('component-render', {
      component: componentName,
      duration,
      timestamp: Date.now()
    });
    
    // 性能警告
    if (import.meta.env.DEV && duration > 100) {
      console.warn(`🐌 慢组件渲染: ${componentName} (${duration}ms)`);
    }
  }

  /**
   * 记录性能指标
   */
  recordMetric(type, data) {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }
    
    const metrics = this.metrics.get(type);
    metrics.push({
      ...data,
      timestamp: data.timestamp || Date.now()
    });
    
    // 限制存储的指标数量，避免内存泄漏
    if (metrics.length > 100) {
      metrics.splice(0, 50); // 删除前50个
    }
  }

  /**
   * 获取资源类型
   */
  getResourceType(url) {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
    return 'other';
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const report = {};
    
    for (const [type, metrics] of this.metrics.entries()) {
      report[type] = {
        count: metrics.length,
        latest: metrics[metrics.length - 1],
        average: this.calculateAverage(metrics),
        summary: this.generateSummary(type, metrics)
      };
    }
    
    return report;
  }

  /**
   * 计算平均值
   */
  calculateAverage(metrics) {
    if (metrics.length === 0) return 0;
    
    const durationKey = metrics[0].duration !== undefined ? 'duration' : 
                       metrics[0].total !== undefined ? 'total' : null;
    
    if (!durationKey) return 0;
    
    const sum = metrics.reduce((acc, metric) => acc + (metric[durationKey] || 0), 0);
    return Math.round(sum / metrics.length);
  }

  /**
   * 生成摘要
   */
  generateSummary(type, metrics) {
    switch (type) {
      case 'api-request':
        return this.summarizeApiRequests(metrics);
      case 'page-load':
        return this.summarizePageLoad(metrics);
      default:
        return null;
    }
  }

  /**
   * API请求摘要
   */
  summarizeApiRequests(metrics) {
    const slowRequests = metrics.filter(m => m.duration > 1000).length;
    const errorRequests = metrics.filter(m => m.status >= 400).length;
    
    return {
      total: metrics.length,
      slow: slowRequests,
      errors: errorRequests,
      successRate: ((metrics.length - errorRequests) / metrics.length * 100).toFixed(1)
    };
  }

  /**
   * 页面加载摘要
   */
  summarizePageLoad(metrics) {
    if (metrics.length === 0) return null;
    
    const latest = metrics[metrics.length - 1];
    return {
      total: latest.total,
      dom: latest.dom,
      load: latest.load
    };
  }

  /**
   * 清理监控器
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;

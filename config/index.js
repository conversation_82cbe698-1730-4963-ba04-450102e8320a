/**
 * 统一配置管理
 * 集中管理所有环境配置和应用设置
 */

const path = require('path');
const fs = require('fs');

// 获取当前环境
const NODE_ENV = process.env.NODE_ENV || 'development';

/**
 * 基础配置
 */
const baseConfig = {
  // 应用信息
  app: {
    name: '光年小卖部',
    version: '1.0.0',
    description: 'B2C电商平台',
    port: process.env.PORT || 3000
  },

  // 环境配置
  env: NODE_ENV,
  isDevelopment: NODE_ENV === 'development',
  isProduction: NODE_ENV === 'production',
  isTest: NODE_ENV === 'test',

  // 路径配置
  paths: {
    root: path.resolve(__dirname, '..'),
    server: path.resolve(__dirname, '../server'),
    uploads: path.resolve(__dirname, '../server/uploads'),
    logs: path.resolve(__dirname, '../server/logs'),
    temp: path.resolve(__dirname, '../temp'),
    config: path.resolve(__dirname, '.'),
    public: path.resolve(__dirname, '../dist')
  },

  // 数据库配置
  database: {
    development: {
      dialect: 'sqlite',
      storage: path.resolve(__dirname, '../server/database.sqlite'),
      logging: console.log
    },
    production: {
      dialect: 'mysql',
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      database: process.env.DB_NAME || 'feishu_mall',
      username: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'password',
      logging: false,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    }
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },

  // 飞书配置
  feishu: {
    appId: process.env.FEISHU_APP_ID,
    appSecret: process.env.FEISHU_APP_SECRET,
    redirectUri: process.env.FEISHU_REDIRECT_URI,
    webhookUrl: process.env.FEISHU_WEBHOOK_URL,
    botToken: process.env.FEISHU_BOT_TOKEN
  },

  // 文件上传配置
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    uploadPath: 'uploads/',
    imagePath: 'uploads/images/',
    tempPath: 'temp/uploads/'
  },

  // 分页配置
  pagination: {
    defaultPage: 1,
    defaultLimit: 20,
    maxLimit: 100
  },

  // 缓存配置
  cache: {
    ttl: 300, // 5分钟
    checkPeriod: 600 // 10分钟检查一次过期
  },

  // 日志配置
  logging: {
    level: NODE_ENV === 'production' ? 'info' : 'debug',
    maxFiles: 10,
    maxSize: '10m',
    datePattern: 'YYYY-MM-DD'
  },

  // 安全配置
  security: {
    bcryptRounds: 12,
    rateLimitWindow: 15 * 60 * 1000, // 15分钟
    rateLimitMax: 100, // 每个IP最多100次请求
    corsOrigins: NODE_ENV === 'production' 
      ? ['http://**************', 'https://**************']
      : ['http://localhost:5173', 'http://localhost:3000']
  },

  // 定时任务配置
  scheduler: {
    timezone: 'Asia/Shanghai',
    dailyReportTime: '0 19 * * 1-5', // 工作日19:00
    weeklyReportTime: '0 9 * * 1',   // 周一9:00
    monthlyReportTime: '0 10 1 * *'  // 每月1日10:00
  }
};

/**
 * 环境特定配置
 */
const envConfigs = {
  development: {
    database: baseConfig.database.development,
    logging: {
      ...baseConfig.logging,
      level: 'debug'
    },
    security: {
      ...baseConfig.security,
      corsOrigins: ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5173']
    }
  },

  production: {
    database: baseConfig.database.production,
    logging: {
      ...baseConfig.logging,
      level: 'info'
    },
    security: {
      ...baseConfig.security,
      corsOrigins: ['http://**************', 'https://**************']
    }
  },

  test: {
    database: {
      ...baseConfig.database.development,
      storage: ':memory:',
      logging: false
    },
    logging: {
      ...baseConfig.logging,
      level: 'error'
    }
  }
};

/**
 * 合并配置
 */
const config = {
  ...baseConfig,
  ...envConfigs[NODE_ENV]
};

/**
 * 配置验证
 */
function validateConfig() {
  const required = ['JWT_SECRET'];
  
  if (NODE_ENV === 'production') {
    required.push(
      'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
      'FEISHU_APP_ID', 'FEISHU_APP_SECRET'
    );
  }

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.warn(`⚠️  缺少环境变量: ${missing.join(', ')}`);
    if (NODE_ENV === 'production') {
      throw new Error(`生产环境缺少必要的环境变量: ${missing.join(', ')}`);
    }
  }
}

/**
 * 创建必要的目录
 */
function ensureDirectories() {
  const dirs = [
    config.paths.uploads,
    config.paths.logs,
    config.paths.temp,
    path.join(config.paths.uploads, 'images'),
    path.join(config.paths.temp, 'uploads'),
    path.join(config.paths.temp, 'logs'),
    path.join(config.paths.temp, 'cache')
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 创建目录: ${dir}`);
    }
  });
}

/**
 * 初始化配置
 */
function init() {
  console.log(`🚀 初始化配置 - 环境: ${NODE_ENV}`);
  
  try {
    validateConfig();
    ensureDirectories();
    console.log('✅ 配置初始化完成');
  } catch (error) {
    console.error('❌ 配置初始化失败:', error.message);
    process.exit(1);
  }
}

// 导出配置和初始化函数
module.exports = {
  ...config,
  init,
  validateConfig,
  ensureDirectories
};

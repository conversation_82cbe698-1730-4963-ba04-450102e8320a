#!/usr/bin/env node

import axios from 'axios';
import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔍 开始系统健康检查...\n');

// 检查项目配置
async function checkSystemHealth() {
  const results = {
    backend: false,
    frontend: false,
    database: false,
    apis: {},
    files: {},
    processes: {}
  };

  // 1. 检查后端服务器
  console.log('📡 检查后端服务器...');
  try {
    const response = await axios.get('http://localhost:3000/api/health', { timeout: 5000 });
    if (response.status === 200) {
      results.backend = true;
      console.log('✅ 后端服务器运行正常');
    }
  } catch (error) {
    console.log('❌ 后端服务器连接失败:', error.message);
  }

  // 2. 检查前端服务器
  console.log('\n🌐 检查前端服务器...');
  try {
    const response = await axios.get('http://localhost:5173', { timeout: 5000 });
    if (response.status === 200) {
      results.frontend = true;
      console.log('✅ 前端服务器运行正常');
    }
  } catch (error) {
    console.log('❌ 前端服务器连接失败:', error.message);
  }

  // 3. 检查关键API端点
  console.log('\n🔌 检查关键API端点...');
  const apiEndpoints = [
    '/api/health',
    '/api/products',
    '/api/categories',
    '/api/users',
    '/api-docs'
  ];

  for (const endpoint of apiEndpoints) {
    try {
      const response = await axios.get(`http://localhost:3000${endpoint}`, { timeout: 3000 });
      results.apis[endpoint] = response.status === 200;
      console.log(`${response.status === 200 ? '✅' : '❌'} ${endpoint}: ${response.status}`);
    } catch (error) {
      results.apis[endpoint] = false;
      console.log(`❌ ${endpoint}: ${error.response?.status || 'TIMEOUT'}`);
    }
  }

  // 4. 检查关键文件
  console.log('\n📁 检查关键文件...');
  const criticalFiles = [
    'package.json',
    'server/package.json',
    'server/server.js',
    'server/.env',
    '.env',
    'src/main.js',
    'index.html'
  ];

  for (const file of criticalFiles) {
    const exists = fs.existsSync(file);
    results.files[file] = exists;
    console.log(`${exists ? '✅' : '❌'} ${file}`);
  }

  // 5. 检查进程
  console.log('\n⚙️ 检查运行进程...');
  return new Promise((resolve) => {
    exec('ps aux | grep -E "(node|npm)" | grep -v grep', (error, stdout) => {
      if (stdout) {
        const processes = stdout.split('\n').filter(line => line.trim());
        console.log(`✅ 发现 ${processes.length} 个Node.js相关进程`);
        processes.forEach(proc => {
          if (proc.includes('server.js') || proc.includes('vite')) {
            console.log(`   📍 ${proc.split(/\s+/).slice(10).join(' ')}`);
          }
        });
        results.processes.count = processes.length;
      } else {
        console.log('❌ 未发现Node.js进程');
        results.processes.count = 0;
      }

      // 6. 生成报告
      console.log('\n📊 系统健康报告:');
      console.log('==================');
      console.log(`后端服务器: ${results.backend ? '✅ 正常' : '❌ 异常'}`);
      console.log(`前端服务器: ${results.frontend ? '✅ 正常' : '❌ 异常'}`);
      console.log(`API端点: ${Object.values(results.apis).filter(Boolean).length}/${Object.keys(results.apis).length} 正常`);
      console.log(`关键文件: ${Object.values(results.files).filter(Boolean).length}/${Object.keys(results.files).length} 存在`);
      console.log(`运行进程: ${results.processes.count} 个`);

      const overallHealth = results.backend && results.frontend && 
                           Object.values(results.apis).filter(Boolean).length >= 3 &&
                           Object.values(results.files).filter(Boolean).length >= 5;

      console.log(`\n🎯 总体状态: ${overallHealth ? '✅ 健康' : '⚠️ 需要关注'}`);

      if (!overallHealth) {
        console.log('\n🔧 建议修复步骤:');
        if (!results.backend) console.log('   1. 重启后端服务器: cd server && npm start');
        if (!results.frontend) console.log('   2. 重启前端服务器: npm run dev');
        if (Object.values(results.apis).filter(Boolean).length < 3) {
          console.log('   3. 检查API路由配置');
        }
      }

      resolve(results);
    });
  });
}

// 运行检查
checkSystemHealth().then(() => {
  console.log('\n✅ 系统健康检查完成！');
}).catch(error => {
  console.error('❌ 健康检查失败:', error.message);
});

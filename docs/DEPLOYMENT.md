# 光年小卖部 - 部署指南

## 📋 部署概览

本文档提供了光年小卖部项目的完整部署指南，包括开发环境、测试环境和生产环境的部署流程。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)   │    │  后端 (Node.js) │    │  数据库 (MySQL) │
│   Port: 5173    │────│   Port: 3000    │────│   Port: 3306    │
│   (开发环境)     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│  Nginx (生产)   │──────────────┘
                        │   Port: 80/443  │
                        └─────────────────┘
```

## 🔧 环境要求

### 基础环境
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **MySQL**: >= 8.0 (生产环境)
- **Nginx**: >= 1.18 (生产环境)
- **PM2**: >= 5.0 (生产环境)

### 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **内存**: >= 2GB
- **存储**: >= 10GB
- **网络**: 稳定的互联网连接

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd lightyear-mall
```

### 2. 安装依赖
```bash
# 前端依赖
npm install

# 后端依赖
cd server
npm install
cd ..
```

### 3. 环境配置
```bash
# 复制环境变量模板
cp server/.env.example server/.env

# 编辑环境变量
nano server/.env
```

### 4. 数据库初始化
```bash
cd server
npm run migrate
cd ..
```

### 5. 启动开发环境
```bash
# 启动后端
cd server
npm run dev

# 新终端启动前端
npm run dev
```

## 🔨 开发环境部署

### 环境变量配置
创建 `server/.env` 文件：

```env
# 基础配置
NODE_ENV=development
PORT=3000

# 数据库配置 (开发环境使用SQLite)
DB_DIALECT=sqlite
DB_STORAGE=./database.sqlite

# JWT配置
JWT_SECRET=your-development-secret-key
JWT_EXPIRES_IN=24h

# 飞书配置
FEISHU_APP_ID=your-feishu-app-id
FEISHU_APP_SECRET=your-feishu-app-secret
FEISHU_REDIRECT_URI=http://localhost:5173/feishu/callback
```

### 启动命令
```bash
# 方式1: 分别启动
npm run dev          # 前端 (端口5173)
cd server && npm run dev  # 后端 (端口3000)

# 方式2: 使用脚本
./restart.sh         # 重启脚本
```

## 🧪 测试环境部署

### 环境配置
```env
NODE_ENV=test
PORT=3001
DB_DIALECT=sqlite
DB_STORAGE=:memory:
JWT_SECRET=test-secret-key
```

### 部署步骤
```bash
# 1. 构建前端
npm run build

# 2. 运行测试
cd server
npm test

# 3. 启动测试服务
NODE_ENV=test npm start
```

## 🌐 生产环境部署

### 服务器准备

#### 1. 系统更新
```bash
sudo apt update && sudo apt upgrade -y
```

#### 2. 安装Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 3. 安装MySQL
```bash
sudo apt install mysql-server -y
sudo mysql_secure_installation
```

#### 4. 安装Nginx
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
```

#### 5. 安装PM2
```bash
sudo npm install -g pm2
```

### 项目部署

#### 1. 克隆项目
```bash
cd /var/www
sudo git clone <repository-url> lightyear-mall
sudo chown -R $USER:$USER lightyear-mall
cd lightyear-mall
```

#### 2. 安装依赖
```bash
npm install --production
cd server
npm install --production
cd ..
```

#### 3. 环境配置
```bash
sudo cp server/.env.example server/.env
sudo nano server/.env
```

生产环境变量：
```env
NODE_ENV=production
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=feishu_mall
DB_USER=root
DB_PASSWORD=your-mysql-password

# JWT配置
JWT_SECRET=your-production-secret-key
JWT_EXPIRES_IN=24h

# 飞书配置
FEISHU_APP_ID=your-feishu-app-id
FEISHU_APP_SECRET=your-feishu-app-secret
FEISHU_REDIRECT_URI=http://**************/feishu/callback
FEISHU_WEBHOOK_URL=your-webhook-url
FEISHU_BOT_TOKEN=your-bot-token
```

#### 4. 数据库初始化
```bash
# 创建数据库
mysql -u root -p < config/database/create_production_db.sql

# 运行迁移
cd server
npm run migrate
cd ..
```

#### 5. 构建前端
```bash
npm run build
```

#### 6. 配置Nginx
```bash
sudo cp config/nginx/production.conf /etc/nginx/sites-available/lightyear-mall
sudo ln -s /etc/nginx/sites-available/lightyear-mall /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 7. 启动应用
```bash
cd server
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### PM2配置

创建 `server/ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'lightyear-mall',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
```

## 🔄 自动化部署

### 部署脚本

使用项目提供的部署脚本：
```bash
# 生产环境部署
./scripts/deploy/production-deploy.sh

# 构建脚本
./scripts/build/build.sh
```

### CI/CD流程

#### GitHub Actions示例
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        npm ci
        cd server && npm ci
        
    - name: Build
      run: npm run build
      
    - name: Deploy to server
      run: |
        # 部署到服务器的脚本
        ./scripts/deploy/production-deploy.sh
```

## 📊 监控和维护

### 应用监控
```bash
# PM2监控
pm2 monit

# 查看日志
pm2 logs lightyear-mall

# 重启应用
pm2 restart lightyear-mall

# 查看状态
pm2 status
```

### 系统监控
```bash
# 系统资源
htop
df -h
free -h

# Nginx状态
sudo systemctl status nginx

# MySQL状态
sudo systemctl status mysql
```

### 性能监控
```bash
# 运行性能分析
node scripts/maintenance/performance-optimizer.js

# 查看性能报告
cat temp/performance-report.json
```

## 🔧 故障排除

### 常见问题

#### 1. 端口占用
```bash
# 查看端口占用
sudo lsof -i :3000

# 杀死进程
sudo kill -9 <PID>
```

#### 2. 权限问题
```bash
# 修复文件权限
sudo chown -R $USER:$USER /var/www/lightyear-mall
sudo chmod -R 755 /var/www/lightyear-mall
```

#### 3. 数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 重启MySQL
sudo systemctl restart mysql

# 检查连接
mysql -u root -p -e "SHOW DATABASES;"
```

#### 4. Nginx配置错误
```bash
# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

### 日志查看
```bash
# 应用日志
pm2 logs lightyear-mall

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx
sudo journalctl -u mysql
```

## 🔄 更新部署

### 代码更新
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装新依赖
npm install
cd server && npm install && cd ..

# 3. 运行迁移
cd server && npm run migrate && cd ..

# 4. 重新构建
npm run build

# 5. 重启应用
pm2 restart lightyear-mall
```

### 数据库更新
```bash
# 备份数据库
mysqldump -u root -p feishu_mall > backup_$(date +%Y%m%d_%H%M%S).sql

# 运行新迁移
cd server && npm run migrate && cd ..
```

## 📋 部署检查清单

### 部署前检查
- [ ] 环境变量配置正确
- [ ] 数据库连接正常
- [ ] 飞书应用配置完成
- [ ] SSL证书配置（如需要）
- [ ] 防火墙规则设置
- [ ] 备份策略制定

### 部署后验证
- [ ] 应用正常启动
- [ ] API接口可访问
- [ ] 前端页面加载正常
- [ ] 数据库连接正常
- [ ] 飞书登录功能正常
- [ ] 文件上传功能正常
- [ ] 性能指标正常

## 📞 支持

如有部署问题，请参考：
- [故障排除指南](./TROUBLESHOOTING.md)
- [性能优化指南](../PERFORMANCE_OPTIMIZATION_REPORT.md)
- [项目架构文档](../ARCHITECTURE.md)

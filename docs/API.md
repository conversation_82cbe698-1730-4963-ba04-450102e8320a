# 光年小卖部 API 文档

## 📖 概述

光年小卖部是一个基于飞书集成的B2C电商平台，提供商品展示、兑换、用户管理等功能。

**API基础信息**:
- 基础URL: `http://localhost:3000/api` (开发环境)
- 生产URL: `http://**************:3000/api` (生产环境)
- 认证方式: JWT Bearer Token
- 数据格式: JSON

## 🔗 在线文档

- **Swagger UI**: [http://localhost:3000/api-docs](http://localhost:3000/api-docs)
- **API规范**: [http://localhost:3000/api-docs.json](http://localhost:3000/api-docs.json)

## 🔐 认证

### JWT Token认证

大部分API需要JWT认证，在请求头中添加：

```
Authorization: Bearer <your-jwt-token>
```

### 获取Token

```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

**响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin"
    }
  }
}
```

## 📋 API模块

### 1. 认证模块 (Auth)

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | `/auth/login` | 用户登录 | ❌ |
| POST | `/auth/logout` | 用户登出 | ✅ |
| GET | `/auth/profile` | 获取用户信息 | ✅ |
| PUT | `/auth/profile` | 更新用户信息 | ✅ |

### 2. 商品模块 (Products)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/products` | 获取商品列表 | ❌ | - |
| GET | `/products/:id` | 获取商品详情 | ❌ | - |
| POST | `/products` | 创建商品 | ✅ | Admin |
| PUT | `/products/:id` | 更新商品 | ✅ | Admin |
| DELETE | `/products/:id` | 删除商品 | ✅ | Admin |
| GET | `/products/price-ranges` | 获取价格范围 | ❌ | - |
| GET | `/products/popular` | 获取热门商品 | ❌ | - |
| POST | `/products/import` | 批量导入商品 | ✅ | Admin |
| GET | `/products/export` | 导出商品数据 | ✅ | Admin |

### 3. 分类模块 (Categories)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/categories` | 获取分类列表 | ❌ | - |
| GET | `/categories/:id` | 获取分类详情 | ❌ | - |
| POST | `/categories` | 创建分类 | ✅ | Admin |
| PUT | `/categories/:id` | 更新分类 | ✅ | Admin |
| DELETE | `/categories/:id` | 删除分类 | ✅ | Admin |

### 4. 兑换模块 (Exchanges)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/exchanges` | 获取兑换列表 | ✅ | - |
| GET | `/exchanges/:id` | 获取兑换详情 | ✅ | - |
| POST | `/exchanges` | 创建兑换订单 | ✅ | - |
| PUT | `/exchanges/:id` | 更新兑换状态 | ✅ | Admin |
| DELETE | `/exchanges/:id` | 取消兑换 | ✅ | - |

### 5. 用户模块 (Users)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/users` | 获取用户列表 | ✅ | Admin |
| GET | `/users/:id` | 获取用户详情 | ✅ | Admin |
| PUT | `/users/:id` | 更新用户信息 | ✅ | Admin |
| DELETE | `/users/:id` | 删除用户 | ✅ | Admin |
| PUT | `/users/:id/balance` | 更新用户余额 | ✅ | Admin |

### 6. 反馈模块 (Feedback)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/feedback` | 获取反馈列表 | ✅ | Admin |
| GET | `/feedback/:id` | 获取反馈详情 | ✅ | - |
| POST | `/feedback` | 提交反馈 | ✅ | - |
| PUT | `/feedback/:id` | 更新反馈状态 | ✅ | Admin |
| DELETE | `/feedback/:id` | 删除反馈 | ✅ | Admin |

### 7. 公告模块 (Announcements)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/announcements` | 获取公告列表 | ❌ | - |
| GET | `/announcements/:id` | 获取公告详情 | ❌ | - |
| POST | `/announcements` | 创建公告 | ✅ | Admin |
| PUT | `/announcements/:id` | 更新公告 | ✅ | Admin |
| DELETE | `/announcements/:id` | 删除公告 | ✅ | Admin |

### 8. 通知模块 (Notifications)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/notifications` | 获取通知列表 | ✅ | - |
| PUT | `/notifications/:id/read` | 标记已读 | ✅ | - |
| DELETE | `/notifications/:id` | 删除通知 | ✅ | - |

### 9. 系统模块 (System)

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| GET | `/system/health` | 健康检查 | ❌ | - |
| GET | `/system/stats` | 系统统计 | ✅ | Admin |
| GET | `/system/performance` | 性能指标 | ✅ | Admin |
| POST | `/system/cache/clear` | 清理缓存 | ✅ | Admin |

## 📊 数据模型

### 用户 (User)
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "role": "admin",
  "feishuUserId": "ou_xxx",
  "lyBalance": 1000,
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 商品 (Product)
```json
{
  "id": 1,
  "name": "商品名称",
  "description": "商品描述",
  "lyPrice": 100,
  "rmbPrice": 10.00,
  "stock": 50,
  "categoryId": 1,
  "status": "active",
  "isHot": true,
  "isNew": false,
  "exchangeCount": 10,
  "imageUrl": "https://example.com/image.jpg",
  "images": ["https://example.com/image1.jpg"],
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 兑换订单 (Exchange)
```json
{
  "id": 1,
  "orderNumber": "EX20240101001",
  "userId": 1,
  "productId": 1,
  "quantity": 1,
  "totalLyPrice": 100,
  "status": "pending",
  "notes": "备注信息",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

## 🔄 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": { /* 响应数据 */ }
}
```

### 分页响应
```json
{
  "data": [ /* 数据列表 */ ],
  "total": 100,
  "page": 1,
  "limit": 20,
  "totalPages": 5
}
```

### 错误响应
```json
{
  "message": "错误信息",
  "status": 400,
  "error": "详细错误描述"
}
```

## 📝 状态码

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🧪 测试示例

### 获取商品列表
```bash
curl -X GET "http://localhost:3000/api/products?page=1&limit=10&category=1" \
  -H "Content-Type: application/json"
```

### 创建商品
```bash
curl -X POST "http://localhost:3000/api/products" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "name": "测试商品",
    "description": "这是一个测试商品",
    "categoryId": 1,
    "lyPrice": 100,
    "rmbPrice": 10.00,
    "stock": 50
  }'
```

## 📞 支持

如有API使用问题，请联系：
- 邮箱: <EMAIL>
- 文档: [项目文档](../README.md)
- 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

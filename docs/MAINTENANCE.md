# 光年小卖部 - 维护建议

## 📋 概述

本文档提供了光年小卖部项目的日常维护建议，包括定期维护任务、监控策略、备份方案等，确保系统稳定运行。

## 🔄 日常维护任务

### 每日维护 (Daily)

#### 1. 系统健康检查
```bash
# 检查应用状态
pm2 status

# 检查系统资源
free -h
df -h

# 检查错误日志
tail -n 50 /var/log/nginx/error.log
pm2 logs lightyear-mall --lines 50 --err
```

#### 2. 数据库健康检查
```sql
-- 检查数据库连接
SHOW PROCESSLIST;

-- 检查表状态
SHOW TABLE STATUS;

-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

#### 3. 性能监控
```bash
# 运行性能分析
node scripts/maintenance/performance-optimizer.js

# 查看性能报告
cat temp/performance-report.json | jq '.summary'
```

### 每周维护 (Weekly)

#### 1. 日志清理
```bash
# 清理应用日志 (保留30天)
find server/logs/ -name "*.log" -mtime +30 -delete

# 清理Nginx日志
sudo logrotate -f /etc/logrotate.d/nginx

# 清理PM2日志
pm2 flush
```

#### 2. 数据库优化
```sql
-- 分析表结构
ANALYZE TABLE products, users, exchanges, categories;

-- 优化表
OPTIMIZE TABLE products, users, exchanges, categories;

-- 检查索引使用情况
SELECT 
  TABLE_NAME,
  INDEX_NAME,
  CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'feishu_mall';
```

#### 3. 缓存清理
```bash
# 清理API缓存
curl -X POST http://localhost:3000/api/system/cache/clear \
  -H "Authorization: Bearer <admin-token>"

# 清理临时文件
find temp/ -type f -mtime +7 -delete
```

### 每月维护 (Monthly)

#### 1. 安全更新
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 更新Node.js依赖
npm audit
npm audit fix

# 检查安全漏洞
cd server && npm audit
```

#### 2. 数据库维护
```sql
-- 数据库备份
mysqldump -u root -p feishu_mall > backup_$(date +%Y%m%d).sql

-- 检查数据完整性
CHECK TABLE products, users, exchanges, categories;

-- 重建索引
ALTER TABLE products ENGINE=InnoDB;
```

#### 3. 性能优化
```bash
# 分析慢查询
mysqldumpslow /var/log/mysql/slow.log

# 检查磁盘使用
du -sh /var/www/lightyear-mall/*

# 清理旧备份文件
find /backup/ -name "*.sql" -mtime +90 -delete
```

---

## 📊 监控策略

### 应用监控

#### 1. 关键指标
- **响应时间**: < 3秒
- **错误率**: < 5%
- **可用性**: > 99%
- **内存使用**: < 80%
- **CPU使用**: < 70%

#### 2. 监控脚本
```bash
#!/bin/bash
# health-check.sh

# 检查应用状态
if ! curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "应用健康检查失败" | mail -s "应用告警" <EMAIL>
    pm2 restart lightyear-mall
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    echo "内存使用率过高: ${MEMORY_USAGE}%" | mail -s "内存告警" <EMAIL>
fi
```

#### 3. 自动化监控
```bash
# 添加到crontab
crontab -e

# 每5分钟检查一次
*/5 * * * * /path/to/health-check.sh

# 每小时生成性能报告
0 * * * * cd /var/www/lightyear-mall && node scripts/maintenance/performance-optimizer.js
```

### 数据库监控

#### 1. 关键指标
```sql
-- 连接数监控
SHOW STATUS LIKE 'Threads_connected';

-- 查询性能监控
SHOW STATUS LIKE 'Slow_queries';

-- 锁等待监控
SHOW STATUS LIKE 'Table_locks_waited';
```

#### 2. 监控查询
```sql
-- 创建监控视图
CREATE VIEW db_performance AS
SELECT 
    SCHEMA_NAME as database_name,
    SUM(data_length + index_length) / 1024 / 1024 AS size_mb,
    COUNT(*) as table_count
FROM information_schema.TABLES 
WHERE SCHEMA_NAME = 'feishu_mall'
GROUP BY SCHEMA_NAME;
```

---

## 💾 备份策略

### 数据库备份

#### 1. 自动备份脚本
```bash
#!/bin/bash
# backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/database"
DB_NAME="feishu_mall"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u root -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除30天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "数据库备份完成: backup_$DATE.sql.gz"
```

#### 2. 备份计划
```bash
# 每日备份 (凌晨2点)
0 2 * * * /path/to/backup-database.sh

# 每周完整备份 (周日凌晨1点)
0 1 * * 0 /path/to/full-backup.sh
```

### 文件备份

#### 1. 应用文件备份
```bash
#!/bin/bash
# backup-files.sh

DATE=$(date +%Y%m%d)
BACKUP_DIR="/backup/files"
APP_DIR="/var/www/lightyear-mall"

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz $APP_DIR/server/uploads/

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz $APP_DIR/server/.env $APP_DIR/config/

# 删除30天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

#### 2. 恢复流程
```bash
# 数据库恢复
gunzip backup_20240101_120000.sql.gz
mysql -u root -p feishu_mall < backup_20240101_120000.sql

# 文件恢复
tar -xzf uploads_20240101.tar.gz -C /var/www/lightyear-mall/server/
```

---

## 🔧 系统优化

### 性能优化

#### 1. 数据库优化
```sql
-- 配置优化 (my.cnf)
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_size = 128M
max_connections = 200

-- 索引优化
CREATE INDEX idx_products_composite ON products(categoryId, status, createdAt);
CREATE INDEX idx_exchanges_user_status ON exchanges(userId, status);
```

#### 2. 应用优化
```javascript
// PM2配置优化
module.exports = {
  apps: [{
    name: 'lightyear-mall',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
```

#### 3. Nginx优化
```nginx
# nginx.conf优化
worker_processes auto;
worker_connections 1024;

gzip on;
gzip_types text/plain text/css application/json application/javascript;

client_max_body_size 10M;
keepalive_timeout 65;
```

### 安全加固

#### 1. 系统安全
```bash
# 防火墙配置
sudo ufw enable
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443

# 禁用不必要的服务
sudo systemctl disable apache2
sudo systemctl disable sendmail
```

#### 2. 应用安全
```javascript
// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  }
}));

// 限流配置
const rateLimit = require('express-rate-limit');
app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100 // 限制每个IP 100次请求
}));
```

---

## 📈 容量规划

### 存储容量

#### 1. 数据增长预估
```
用户数据: 1KB/用户 × 1000用户 = 1MB
商品数据: 5KB/商品 × 500商品 = 2.5MB
订单数据: 2KB/订单 × 10000订单 = 20MB
图片文件: 100KB/图片 × 1000图片 = 100MB
日志文件: 10MB/月 × 12月 = 120MB

总计: ~250MB (当前) → 预计1年后: ~1GB
```

#### 2. 扩容建议
- **数据库**: 当数据量超过1GB时考虑分库分表
- **文件存储**: 当文件超过10GB时考虑对象存储
- **日志**: 实施日志轮转和归档策略

### 性能容量

#### 1. 并发处理能力
```
当前配置: 2核4GB
支持并发: ~100用户
响应时间: <3秒

扩容建议:
- 4核8GB: ~500用户
- 8核16GB: ~2000用户
```

#### 2. 扩容策略
- **垂直扩容**: 增加CPU和内存
- **水平扩容**: 负载均衡多实例
- **数据库扩容**: 读写分离、主从复制

---

## 🚨 应急预案

### 故障响应流程

#### 1. 服务不可用
```bash
# 1. 快速诊断
pm2 status
curl http://localhost:3000/api/health

# 2. 重启服务
pm2 restart lightyear-mall

# 3. 检查日志
pm2 logs lightyear-mall --err

# 4. 通知相关人员
echo "服务故障" | mail -s "紧急告警" <EMAIL>
```

#### 2. 数据库故障
```bash
# 1. 检查数据库状态
sudo systemctl status mysql

# 2. 重启数据库
sudo systemctl restart mysql

# 3. 数据恢复
mysql -u root -p feishu_mall < latest_backup.sql
```

#### 3. 磁盘空间不足
```bash
# 1. 清理日志文件
find /var/log -name "*.log" -mtime +7 -delete

# 2. 清理临时文件
rm -rf /tmp/*

# 3. 清理应用缓存
rm -rf temp/*
```

### 联系信息

#### 紧急联系人
- **技术负责人**: +86-xxx-xxxx-xxxx
- **运维负责人**: +86-xxx-xxxx-xxxx
- **产品负责人**: +86-xxx-xxxx-xxxx

#### 外部服务
- **云服务商**: 400-xxx-xxxx
- **域名服务商**: 400-xxx-xxxx
- **CDN服务商**: 400-xxx-xxxx

---

## 📚 维护文档

### 操作手册
- [部署指南](./DEPLOYMENT.md)
- [故障排除](./TROUBLESHOOTING.md)
- [API文档](./API.md)

### 变更记录
- 维护所有系统变更的详细记录
- 记录配置修改和版本更新
- 保存故障处理过程和解决方案

### 知识库
- 常见问题解答
- 最佳实践总结
- 经验教训记录

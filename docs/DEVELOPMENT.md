# 光年小卖部 - 开发规范

## 📋 概述

本文档定义了光年小卖部项目的开发规范，包括代码规范、提交规范、分支管理等，旨在提高代码质量和团队协作效率。

## 🏗️ 项目结构规范

### 目录结构
```
lightyear-mall/
├── src/                    # 前端源码
│   ├── api/               # API接口
│   ├── components/        # 公共组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   └── views/             # 页面组件
├── server/                # 后端源码
│   ├── config/            # 配置文件
│   ├── controllers/       # 控制器
│   ├── middlewares/       # 中间件
│   ├── models/            # 数据模型
│   ├── routes/            # 路由定义
│   ├── services/          # 业务服务
│   └── utils/             # 工具函数
├── config/                # 项目配置
├── scripts/               # 脚本工具
├── docs/                  # 项目文档
└── temp/                  # 临时文件
```

### 文件命名规范
- **组件文件**: PascalCase (如: `UserProfile.vue`)
- **工具文件**: camelCase (如: `formatUtils.js`)
- **配置文件**: kebab-case (如: `database-config.js`)
- **样式文件**: kebab-case (如: `user-profile.scss`)

## 💻 代码规范

### JavaScript/TypeScript规范

#### 1. 变量命名
```javascript
// ✅ 好的命名
const userProfile = {};
const isAuthenticated = true;
const MAX_RETRY_COUNT = 3;

// ❌ 避免的命名
const data = {};
const flag = true;
const num = 3;
```

#### 2. 函数命名
```javascript
// ✅ 动词开头，描述功能
function getUserProfile() {}
function validateEmail() {}
function handleSubmit() {}

// ❌ 避免模糊命名
function process() {}
function handle() {}
function do() {}
```

#### 3. 常量定义
```javascript
// ✅ 全大写，下划线分隔
const API_BASE_URL = 'http://localhost:3000/api';
const DEFAULT_PAGE_SIZE = 20;
const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user'
};
```

#### 4. 函数规范
```javascript
// ✅ 单一职责，参数不超过3个
function createUser(userData) {
  // 函数体不超过20行
  return userService.create(userData);
}

// ✅ 使用解构参数
function updateUser({ id, name, email }) {
  return userService.update(id, { name, email });
}

// ✅ 异步函数使用async/await
async function fetchUserData(userId) {
  try {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('获取用户数据失败:', error);
    throw error;
  }
}
```

#### 5. 错误处理
```javascript
// ✅ 统一错误处理
try {
  const result = await someAsyncOperation();
  return result;
} catch (error) {
  console.error('操作失败:', error);
  throw new Error(`操作失败: ${error.message}`);
}

// ✅ 使用自定义错误类
class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}
```

### Vue.js规范

#### 1. 组件命名
```vue
<!-- ✅ PascalCase -->
<template>
  <UserProfile :user="currentUser" />
  <ProductList :products="products" />
</template>

<!-- ❌ 避免 -->
<template>
  <userprofile :user="currentUser" />
  <product-list :products="products" />
</template>
```

#### 2. Props定义
```vue
<script>
export default {
  name: 'UserProfile',
  props: {
    // ✅ 详细的props定义
    user: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.id !== 'undefined';
      }
    },
    showActions: {
      type: Boolean,
      default: true
    }
  }
}
</script>
```

#### 3. 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 导入
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

export default {
  name: 'ComponentName',
  components: {
    // 组件注册
  },
  props: {
    // Props定义
  },
  emits: ['update', 'delete'],
  setup(props, { emit }) {
    // 组合式API逻辑
    return {
      // 返回响应式数据和方法
    };
  }
}
</script>

<style scoped>
/* 样式 */
</style>
```

### Node.js规范

#### 1. 模块导入
```javascript
// ✅ 按类型分组导入
// 内置模块
const fs = require('fs');
const path = require('path');

// 第三方模块
const express = require('express');
const bcrypt = require('bcryptjs');

// 本地模块
const userService = require('../services/userService');
const { validateEmail } = require('../utils/validation');
```

#### 2. 控制器规范
```javascript
// ✅ 控制器结构
class UserController {
  /**
   * 获取用户列表
   * @swagger
   * /users:
   *   get:
   *     summary: 获取用户列表
   */
  async getUsers(req, res) {
    try {
      const { page = 1, limit = 20 } = req.query;
      const users = await userService.getUsers({ page, limit });
      
      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      console.error('获取用户列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户列表失败',
        error: error.message
      });
    }
  }
}
```

#### 3. 中间件规范
```javascript
// ✅ 中间件结构
const authenticate = (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        message: '访问令牌缺失'
      });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({
      message: '无效的访问令牌'
    });
  }
};
```

## 📝 注释规范

### JSDoc注释
```javascript
/**
 * 创建新用户
 * @param {Object} userData - 用户数据
 * @param {string} userData.username - 用户名
 * @param {string} userData.email - 邮箱
 * @param {string} userData.password - 密码
 * @returns {Promise<Object>} 创建的用户对象
 * @throws {ValidationError} 当用户数据无效时
 * @example
 * const user = await createUser({
 *   username: 'john',
 *   email: '<EMAIL>',
 *   password: 'password123'
 * });
 */
async function createUser(userData) {
  // 实现逻辑
}
```

### Vue组件注释
```vue
<template>
  <!-- 用户资料卡片组件 -->
  <div class="user-profile-card">
    <!-- 用户头像 -->
    <img :src="user.avatar" :alt="user.name" />
    
    <!-- 用户信息 -->
    <div class="user-info">
      <h3>{{ user.name }}</h3>
      <p>{{ user.email }}</p>
    </div>
  </div>
</template>
```

## 🔄 Git工作流规范

### 分支命名规范
```
main                    # 主分支
develop                 # 开发分支
feature/user-auth      # 功能分支
bugfix/login-error     # 修复分支
hotfix/security-patch  # 热修复分支
release/v1.2.0         # 发布分支
```

### 提交信息规范

#### Conventional Commits格式
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 提交示例
```bash
# ✅ 好的提交信息
feat(auth): 添加飞书登录功能
fix(api): 修复用户列表分页问题
docs(readme): 更新安装说明
style(components): 统一组件样式格式
refactor(utils): 重构日期处理工具函数
perf(database): 优化商品查询性能
test(auth): 添加登录功能单元测试
chore(deps): 更新依赖包版本

# ❌ 避免的提交信息
fix bug
update code
add feature
```

#### 详细提交示例
```
feat(products): 添加商品批量导入功能

- 支持CSV和Excel文件格式
- 添加数据验证和错误处理
- 实现导入进度显示
- 添加导入结果统计

Closes #123
```

### 分支管理流程

#### 1. 功能开发流程
```bash
# 1. 从develop创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat(scope): 添加新功能"

# 3. 推送分支
git push origin feature/new-feature

# 4. 创建Pull Request
# 5. 代码审查
# 6. 合并到develop
```

#### 2. 发布流程
```bash
# 1. 从develop创建发布分支
git checkout develop
git checkout -b release/v1.2.0

# 2. 版本号更新和最终测试
# 3. 合并到main和develop
git checkout main
git merge release/v1.2.0
git tag v1.2.0

git checkout develop
git merge release/v1.2.0
```

## 🧪 测试规范

### 单元测试
```javascript
// ✅ 测试文件命名: *.test.js
// tests/utils/validation.test.js

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('should return true for valid email', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
    });
    
    it('should return false for invalid email', () => {
      expect(validateEmail('invalid-email')).toBe(false);
    });
  });
});
```

### API测试
```javascript
// ✅ API测试示例
describe('User API', () => {
  it('should create user successfully', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const response = await request(app)
      .post('/api/users')
      .send(userData)
      .expect(201);
      
    expect(response.body.success).toBe(true);
    expect(response.body.data.username).toBe(userData.username);
  });
});
```

## 📊 代码质量检查

### ESLint配置
```json
{
  "extends": [
    "eslint:recommended",
    "@vue/eslint-config-prettier"
  ],
  "rules": {
    "no-console": "warn",
    "no-unused-vars": "error",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### Prettier配置
```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100
}
```

## 📋 代码审查清单

### 功能性检查
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误处理是否完善
- [ ] 性能是否可接受

### 代码质量检查
- [ ] 命名是否清晰
- [ ] 函数是否单一职责
- [ ] 代码是否可读
- [ ] 是否有重复代码

### 安全性检查
- [ ] 输入验证是否充分
- [ ] 权限控制是否正确
- [ ] 敏感信息是否泄露
- [ ] SQL注入防护

### 文档检查
- [ ] 注释是否充分
- [ ] API文档是否更新
- [ ] README是否更新
- [ ] 变更日志是否记录

## 🔧 开发工具配置

### VS Code配置
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.vue": "vue"
  }
}
```

### 推荐插件
- Vue Language Features (Volar)
- ESLint
- Prettier
- GitLens
- Thunder Client (API测试)

## 📞 支持

如有开发规范问题，请参考：
- [项目架构文档](../ARCHITECTURE.md)
- [API文档](./API.md)
- [部署指南](./DEPLOYMENT.md)

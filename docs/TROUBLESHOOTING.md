# 光年小卖部 - 故障排除指南

## 🔍 概述

本文档提供了光年小卖部项目常见问题的诊断和解决方案，帮助开发者快速定位和解决问题。

## 🚨 常见问题分类

### 1. 启动问题
### 2. 数据库问题
### 3. 认证问题
### 4. API问题
### 5. 前端问题
### 6. 部署问题
### 7. 性能问题

---

## 🚀 启动问题

### 问题1: 端口占用
**症状**: `Error: listen EADDRINUSE :::3000`

**解决方案**:
```bash
# 查看端口占用
sudo lsof -i :3000

# 杀死占用进程
sudo kill -9 <PID>

# 或使用重启脚本
./restart.sh
```

### 问题2: 依赖安装失败
**症状**: `npm install` 报错

**解决方案**:
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version  # 需要 >= 16.0.0
```

### 问题3: 环境变量缺失
**症状**: `JWT_SECRET is not defined`

**解决方案**:
```bash
# 检查.env文件是否存在
ls -la server/.env

# 复制模板文件
cp server/.env.example server/.env

# 编辑环境变量
nano server/.env
```

---

## 🗄️ 数据库问题

### 问题1: 数据库连接失败
**症状**: `SequelizeConnectionError: connect ECONNREFUSED`

**诊断步骤**:
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查连接参数
mysql -h localhost -u root -p

# 检查环境变量
cat server/.env | grep DB_
```

**解决方案**:
```bash
# 启动MySQL服务
sudo systemctl start mysql

# 重置MySQL密码
sudo mysql_secure_installation

# 创建数据库
mysql -u root -p < config/database/create_production_db.sql
```

### 问题2: 迁移失败
**症状**: `Migration failed: Table already exists`

**解决方案**:
```bash
# 查看迁移状态
cd server
npx sequelize-cli db:migrate:status

# 回滚迁移
npx sequelize-cli db:migrate:undo

# 重新运行迁移
npx sequelize-cli db:migrate
```

### 问题3: 数据库权限问题
**症状**: `Access denied for user`

**解决方案**:
```sql
-- 登录MySQL
mysql -u root -p

-- 创建用户并授权
CREATE USER 'feishu_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON feishu_mall.* TO 'feishu_user'@'localhost';
FLUSH PRIVILEGES;
```

---

## 🔐 认证问题

### 问题1: JWT Token无效
**症状**: `JsonWebTokenError: invalid token`

**诊断步骤**:
```bash
# 检查JWT_SECRET配置
echo $JWT_SECRET

# 检查token格式
curl -H "Authorization: Bearer <token>" http://localhost:3000/api/auth/profile
```

**解决方案**:
```javascript
// 检查token生成逻辑
const token = jwt.sign(
  { userId: user.id, role: user.role },
  process.env.JWT_SECRET,
  { expiresIn: '24h' }
);
```

### 问题2: 飞书登录失败
**症状**: 飞书回调返回错误

**诊断步骤**:
```bash
# 检查飞书配置
cat server/.env | grep FEISHU_

# 检查回调URL
curl "https://open.feishu.cn/open-apis/authen/v1/index?app_id=<APP_ID>&redirect_uri=<REDIRECT_URI>"
```

**解决方案**:
1. 确认飞书应用配置正确
2. 检查重定向URI是否匹配
3. 验证应用权限设置

### 问题3: 权限验证失败
**症状**: `403 Forbidden`

**解决方案**:
```javascript
// 检查用户角色
const user = await User.findByPk(userId);
console.log('User role:', user.role);

// 检查中间件逻辑
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: '权限不足' });
  }
  next();
};
```

---

## 🌐 API问题

### 问题1: API响应慢
**症状**: 请求超时或响应时间过长

**诊断步骤**:
```bash
# 运行性能分析
node scripts/maintenance/performance-optimizer.js

# 查看慢查询日志
tail -f server/logs/slow-queries.log

# 检查数据库性能
EXPLAIN SELECT * FROM products WHERE categoryId = 1;
```

**解决方案**:
```sql
-- 添加索引
CREATE INDEX idx_products_category ON products(categoryId);
CREATE INDEX idx_products_status ON products(status);

-- 优化查询
SELECT p.*, c.name as categoryName 
FROM products p 
LEFT JOIN categories c ON p.categoryId = c.id 
WHERE p.status = 'active' 
LIMIT 20;
```

### 问题2: CORS错误
**症状**: `Access-Control-Allow-Origin` 错误

**解决方案**:
```javascript
// 检查CORS配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['http://**************'] 
    : ['http://localhost:5173'],
  credentials: true
}));
```

### 问题3: 文件上传失败
**症状**: 文件上传返回错误

**诊断步骤**:
```bash
# 检查上传目录权限
ls -la server/uploads/

# 检查磁盘空间
df -h

# 检查文件大小限制
grep -r "fileSize" server/
```

**解决方案**:
```bash
# 创建上传目录
mkdir -p server/uploads/images

# 设置权限
chmod 755 server/uploads/
chown -R $USER:$USER server/uploads/
```

---

## 🎨 前端问题

### 问题1: 页面白屏
**症状**: 页面加载后显示空白

**诊断步骤**:
```bash
# 检查控制台错误
# 打开浏览器开发者工具查看Console

# 检查网络请求
# 查看Network标签页

# 检查构建文件
ls -la dist/
```

**解决方案**:
```bash
# 重新构建
npm run build

# 检查路由配置
# 确认路由路径正确

# 检查API连接
curl http://localhost:3000/api/health
```

### 问题2: 样式丢失
**症状**: 页面样式不正确

**解决方案**:
```bash
# 检查CSS文件是否正确加载
# 清理浏览器缓存
# 重新构建项目
npm run build
```

### 问题3: 路由跳转失败
**症状**: 页面跳转不工作

**解决方案**:
```javascript
// 检查路由配置
const router = createRouter({
  history: createWebHistory(),
  routes: [
    // 确认路由配置正确
  ]
});

// 检查导航守卫
router.beforeEach((to, from, next) => {
  // 确认守卫逻辑正确
  next();
});
```

---

## 🚀 部署问题

### 问题1: Nginx配置错误
**症状**: 502 Bad Gateway

**诊断步骤**:
```bash
# 测试Nginx配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 检查上游服务
curl http://localhost:3000/api/health
```

**解决方案**:
```bash
# 重新加载配置
sudo systemctl reload nginx

# 重启Nginx
sudo systemctl restart nginx

# 检查防火墙
sudo ufw status
```

### 问题2: PM2进程异常
**症状**: 应用进程崩溃

**诊断步骤**:
```bash
# 查看PM2状态
pm2 status

# 查看日志
pm2 logs lightyear-mall

# 查看错误日志
pm2 logs lightyear-mall --err
```

**解决方案**:
```bash
# 重启应用
pm2 restart lightyear-mall

# 重新加载配置
pm2 reload ecosystem.config.js

# 查看详细信息
pm2 describe lightyear-mall
```

### 问题3: SSL证书问题
**症状**: HTTPS访问失败

**解决方案**:
```bash
# 检查证书有效期
openssl x509 -in /path/to/cert.pem -text -noout

# 更新Let's Encrypt证书
sudo certbot renew

# 重新加载Nginx
sudo systemctl reload nginx
```

---

## ⚡ 性能问题

### 问题1: 内存使用过高
**症状**: 服务器内存不足

**诊断步骤**:
```bash
# 查看内存使用
free -h
htop

# 查看Node.js进程内存
pm2 monit

# 运行性能分析
node scripts/maintenance/performance-optimizer.js
```

**解决方案**:
```bash
# 重启应用释放内存
pm2 restart lightyear-mall

# 调整PM2配置
# 在ecosystem.config.js中设置max_memory_restart
```

### 问题2: 数据库查询慢
**症状**: 查询响应时间长

**解决方案**:
```sql
-- 分析慢查询
SHOW PROCESSLIST;
SHOW FULL PROCESSLIST;

-- 添加索引
CREATE INDEX idx_products_search ON products(name, status);

-- 优化查询
EXPLAIN SELECT * FROM products WHERE name LIKE '%keyword%';
```

---

## 🛠️ 调试工具

### 日志查看
```bash
# 应用日志
pm2 logs lightyear-mall

# 系统日志
sudo journalctl -u nginx
sudo journalctl -u mysql

# 自定义日志
tail -f server/logs/app.log
```

### 性能监控
```bash
# 系统监控
htop
iotop
nethogs

# 应用监控
pm2 monit

# 数据库监控
mysqladmin processlist
mysqladmin status
```

### 网络诊断
```bash
# 端口检查
netstat -tulpn | grep :3000

# 连接测试
telnet localhost 3000
curl -I http://localhost:3000/api/health

# DNS检查
nslookup domain.com
dig domain.com
```

---

## 📞 获取帮助

### 联系方式
- **技术支持**: <EMAIL>
- **文档**: [项目文档](../README.md)
- **API文档**: [API指南](./API.md)

### 报告问题
1. 收集错误信息和日志
2. 记录重现步骤
3. 提供环境信息
4. 创建GitHub Issue

### 紧急联系
对于生产环境的紧急问题：
1. 立即联系技术负责人
2. 记录问题详情
3. 执行应急预案
4. 事后进行问题分析

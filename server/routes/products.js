/**
 * @swagger
 * tags:
 *   name: Products
 *   description: 商品管理API
 */

const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { authenticate } = require('../middlewares/authMiddleware');
const { isAdmin } = require('../middlewares/admin');
const multer = require('multer');
const path = require('path');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'import-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 限制10MB
  fileFilter: function (req, file, cb) {
    // 只接受csv和excel文件
    const filetypes = /csv|xlsx|xls/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);
    
    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('只支持上传CSV和Excel文件'));
    }
  } 
});

/**
 * @swagger
 * /products:
 *   get:
 *     summary: 获取商品列表
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: category
 *         schema:
 *           type: integer
 *         description: 分类ID
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *         description: 商品状态
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [default, ly-asc, ly-desc, rmb-asc, rmb-desc, newest, oldest, name-asc, name-desc]
 *         description: 排序方式
 *     responses:
 *       200:
 *         description: 成功获取商品列表
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Product'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/', productController.getProducts);

// 获取商品价格范围（用于滑块初始化）
router.get('/price-ranges', productController.getProductPriceRanges);

// 获取热门商品
router.get('/popular', productController.getPopularProducts);

// 导出商品数据
router.get('/export', authenticate, isAdmin, productController.exportProducts);

// 导入商品数据
router.post('/import', authenticate, isAdmin, upload.single('file'), productController.importProducts);

// 批量删除商品
router.post('/bulk-delete', authenticate, isAdmin, productController.bulkDeleteProducts);

/**
 * @swagger
 * /products/{id}:
 *   get:
 *     summary: 获取商品详情
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品ID
 *     responses:
 *       200:
 *         description: 成功获取商品详情
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       404:
 *         description: 商品不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/:id', productController.getProductById);

/**
 * @swagger
 * /products:
 *   post:
 *     summary: 创建新商品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - categoryId
 *               - lyPrice
 *               - rmbPrice
 *             properties:
 *               name:
 *                 type: string
 *                 description: 商品名称
 *               description:
 *                 type: string
 *                 description: 商品描述
 *               categoryId:
 *                 type: integer
 *                 description: 分类ID
 *               lyPrice:
 *                 type: number
 *                 description: 光年币价格
 *               rmbPrice:
 *                 type: number
 *                 description: 人民币价格
 *               stock:
 *                 type: integer
 *                 description: 库存数量
 *               isHot:
 *                 type: boolean
 *                 description: 是否热门
 *               isNew:
 *                 type: boolean
 *                 description: 是否新品
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 description: 商品状态
 *     responses:
 *       201:
 *         description: 商品创建成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: 权限不足
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/', authenticate, isAdmin, productController.createProduct);

// 更新商品信息
router.put('/:id', authenticate, isAdmin, productController.updateProduct);

// 删除商品
router.delete('/:id', authenticate, isAdmin, productController.deleteProduct);

// 更新商品状态（上/下线）
router.put('/:id/status', authenticate, isAdmin, productController.updateProductStatus);

module.exports = router; 
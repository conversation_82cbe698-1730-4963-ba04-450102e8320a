const express = require('express');
const router = express.Router();

// 路由配置 - 统一管理所有API路由
const routeConfigs = [
  { path: '/auth', module: require('./auth') },
  { path: '/products', module: require('./products') },
  { path: '/product-images', module: require('./productImages') },
  { path: '/categories', module: require('./categories') },
  { path: '/announcements', module: require('./announcements') },
  { path: '/users', module: require('./users') },
  { path: '/upload', module: require('./upload') },
  { path: '/exchanges', module: require('./exchanges') },
  { path: '/feedback', module: require('./feedback') },
  { path: '/notifications', module: require('./notification') },
  { path: '/logs', module: require('./logs') },
  { path: '/exports', module: require('./exports') },
  { path: '/system', module: require('./system') },
  { path: '/feishu', module: require('./feishu') },
  { path: '', module: require('./api') } // API路由放在最后，避免路径冲突
];

// 健康检查接口
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: '服务器运行正常',
    timestamp: new Date().toISOString(),
    routes: routeConfigs.length
  });
});

// 自动注册所有路由
routeConfigs.forEach(({ path, module }) => {
  try {
    router.use(path, module);
    console.log(`✅ 路由已注册: /api${path}`);
  } catch (error) {
    console.error(`❌ 路由注册失败: /api${path}`, error.message);
  }
});

module.exports = router;
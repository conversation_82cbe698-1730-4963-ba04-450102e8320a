/**
 * 数据库查询优化工具
 * 提供查询缓存、分页优化、索引建议等功能
 */

const { Op } = require('sequelize');

class QueryOptimizer {
  constructor() {
    this.queryCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    this.slowQueryThreshold = 1000; // 1秒
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(model, options) {
    const modelName = model.name;
    const optionsStr = JSON.stringify(options, (key, value) => {
      if (key === 'transaction') return undefined;
      return value;
    });
    return `${modelName}:${Buffer.from(optionsStr).toString('base64')}`;
  }

  /**
   * 缓存查询结果
   */
  async cachedQuery(model, options, cacheTime = this.cacheTimeout) {
    const cacheKey = this.generateCacheKey(model, options);
    
    // 检查缓存
    const cached = this.queryCache.get(cacheKey);
    if (cached && Date.now() < cached.expiry) {
      console.log(`📦 使用查询缓存: ${model.name}`);
      return cached.data;
    }

    // 执行查询并记录性能
    const startTime = Date.now();
    const result = await model.findAll(options);
    const duration = Date.now() - startTime;

    // 性能警告
    if (duration > this.slowQueryThreshold) {
      console.warn(`🐌 慢查询检测: ${model.name} (${duration}ms)`);
      console.warn('查询选项:', JSON.stringify(options, null, 2));
    }

    // 缓存结果
    this.queryCache.set(cacheKey, {
      data: result,
      expiry: Date.now() + cacheTime
    });

    // 限制缓存大小
    if (this.queryCache.size > 100) {
      const firstKey = this.queryCache.keys().next().value;
      this.queryCache.delete(firstKey);
    }

    return result;
  }

  /**
   * 优化分页查询
   */
  optimizePagination(options, page = 1, limit = 20) {
    const maxLimit = 100;
    const safeLimit = Math.min(limit, maxLimit);
    const offset = (page - 1) * safeLimit;

    return {
      ...options,
      limit: safeLimit,
      offset,
      // 添加默认排序以确保一致性
      order: options.order || [['createdAt', 'DESC']]
    };
  }

  /**
   * 优化关联查询
   */
  optimizeIncludes(includes) {
    if (!includes) return undefined;

    return includes.map(include => ({
      ...include,
      // 只选择必要的字段
      attributes: include.attributes || { exclude: ['createdAt', 'updatedAt'] },
      // 避免深层嵌套
      required: include.required !== undefined ? include.required : false
    }));
  }

  /**
   * 构建搜索查询
   */
  buildSearchQuery(searchTerm, searchFields) {
    if (!searchTerm || !searchFields.length) return {};

    const searchConditions = searchFields.map(field => ({
      [field]: {
        [Op.like]: `%${searchTerm}%`
      }
    }));

    return {
      [Op.or]: searchConditions
    };
  }

  /**
   * 构建日期范围查询
   */
  buildDateRangeQuery(field, startDate, endDate) {
    const conditions = {};
    
    if (startDate || endDate) {
      conditions[field] = {};
      
      if (startDate) {
        conditions[field][Op.gte] = new Date(startDate);
      }
      
      if (endDate) {
        conditions[field][Op.lte] = new Date(endDate);
      }
    }
    
    return conditions;
  }

  /**
   * 批量查询优化
   */
  async batchQuery(model, ids, options = {}) {
    if (!ids.length) return [];

    // 分批查询，避免IN查询过大
    const batchSize = 100;
    const results = [];

    for (let i = 0; i < ids.length; i += batchSize) {
      const batchIds = ids.slice(i, i + batchSize);
      const batchOptions = {
        ...options,
        where: {
          ...options.where,
          id: {
            [Op.in]: batchIds
          }
        }
      };

      const batchResult = await this.cachedQuery(model, batchOptions);
      results.push(...batchResult);
    }

    return results;
  }

  /**
   * 清除相关缓存
   */
  clearCache(pattern) {
    for (const key of this.queryCache.keys()) {
      if (key.includes(pattern)) {
        this.queryCache.delete(key);
      }
    }
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.queryCache.clear();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.queryCache.size,
      keys: Array.from(this.queryCache.keys())
    };
  }

  /**
   * 数据库索引建议
   */
  getIndexSuggestions() {
    return {
      products: [
        'CREATE INDEX idx_products_category ON products(categoryId)',
        'CREATE INDEX idx_products_status ON products(status)',
        'CREATE INDEX idx_products_created ON products(createdAt)',
        'CREATE INDEX idx_products_name_search ON products(name)',
        'CREATE INDEX idx_products_composite ON products(categoryId, status, createdAt)'
      ],
      exchanges: [
        'CREATE INDEX idx_exchanges_user ON exchanges(userId)',
        'CREATE INDEX idx_exchanges_product ON exchanges(productId)',
        'CREATE INDEX idx_exchanges_status ON exchanges(status)',
        'CREATE INDEX idx_exchanges_created ON exchanges(createdAt)',
        'CREATE INDEX idx_exchanges_composite ON exchanges(userId, status, createdAt)'
      ],
      users: [
        'CREATE INDEX idx_users_feishu ON users(feishuUserId)',
        'CREATE INDEX idx_users_role ON users(role)',
        'CREATE INDEX idx_users_active ON users(isActive)',
        'CREATE INDEX idx_users_created ON users(createdAt)'
      ],
      categories: [
        'CREATE INDEX idx_categories_name ON categories(name)',
        'CREATE INDEX idx_categories_active ON categories(isActive)'
      ],
      feedback: [
        'CREATE INDEX idx_feedback_user ON feedback(userId)',
        'CREATE INDEX idx_feedback_status ON feedback(status)',
        'CREATE INDEX idx_feedback_created ON feedback(createdAt)'
      ]
    };
  }

  /**
   * 查询性能分析
   */
  async analyzeQuery(model, options) {
    const startTime = Date.now();
    
    // 执行EXPLAIN查询（MySQL）
    try {
      const explainResult = await model.sequelize.query(
        `EXPLAIN ${model.sequelize.dialect.queryGenerator.selectQuery(
          model.tableName,
          options,
          model
        )}`,
        { type: model.sequelize.QueryTypes.SELECT }
      );
      
      const duration = Date.now() - startTime;
      
      return {
        duration,
        explain: explainResult,
        suggestions: this.generateOptimizationSuggestions(explainResult)
      };
    } catch (error) {
      console.warn('查询分析失败:', error.message);
      return null;
    }
  }

  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions(explainResult) {
    const suggestions = [];
    
    explainResult.forEach(row => {
      // 检查是否使用了索引
      if (row.key === null) {
        suggestions.push('考虑为查询字段添加索引');
      }
      
      // 检查是否有全表扫描
      if (row.type === 'ALL') {
        suggestions.push('避免全表扫描，添加WHERE条件或索引');
      }
      
      // 检查扫描行数
      if (row.rows > 1000) {
        suggestions.push('扫描行数过多，考虑优化查询条件');
      }
    });
    
    return suggestions;
  }
}

// 创建全局实例
const queryOptimizer = new QueryOptimizer();

module.exports = queryOptimizer;

====== 服务器启动环境变量 ======
NODE_ENV: development
是否本地开发环境: 是
SERVER_URL (自动检测后): http://localhost:3000
FEISHU_REDIRECT_URI: http://localhost:3000/api/feishu/callback
====== 环境变量输出结束 ======
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码图片目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
图片上传目录权限正常
静态文件路径1: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
静态文件路径2: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
上传目录绝对路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
=== 飞书配置加载 ===
NODE_ENV: development
FEISHU_REDIRECT_URI: http://localhost:3000/api/feishu/callback
process.env: {
  NODE_ENV: 'development',
  PORT: '3000',
  FEISHU_APP_ID: undefined,
  FEISHU_APP_SECRET: undefined,
  FEISHU_REDIRECT_URI: 'http://localhost:3000/api/feishu/callback'
}
计算得到的redirectUri: http://localhost:3000/api/feishu/callback
✅ 路由已注册: /api/auth
✅ 路由已注册: /api/products
✅ 路由已注册: /api/product-images
✅ 路由已注册: /api/categories
✅ 路由已注册: /api/announcements
✅ 路由已注册: /api/users
✅ 路由已注册: /api/upload
✅ 路由已注册: /api/exchanges
✅ 路由已注册: /api/feedback
✅ 路由已注册: /api/notifications
✅ 路由已注册: /api/logs
✅ 路由已注册: /api/exports
✅ 路由已注册: /api/system
✅ 路由已注册: /api/feishu
✅ 路由已注册: /api
==================================
服务器启动成功，端口: 3000
API地址: http://localhost:3000/api
上传API: http://localhost:3000/api/upload
静态文件: http://localhost:3000/uploads
==================================
初始化定时报告服务...
✅ 定时报告服务初始化完成
📊 定时统计报告服务已启动
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT 1+1 AS result Elapsed time: 1ms
Database connection has been established successfully.
[0mGET /api/health [32m200[0m 1.522 ms - 49[0m
[0mGET /api/env-check [32m200[0m 0.409 ms - 151[0m
开始执行统计查询...
日期范围: 2025-05-07T05:20:15.015Z 至 2025-06-06T05:20:15.015Z
Executed (default): SELECT DATE(`createdAt`) AS `date`, count(`id`) AS `count` FROM `logs` AS `Log` WHERE `Log`.`createdAt` BETWEEN '2025-05-07 13:20:15' AND '2025-06-06 13:20:15' GROUP BY DATE(`createdAt`) ORDER BY DATE(`createdAt`) ASC; Elapsed time: 2ms
日期统计查询成功: 5
Executed (default): SELECT `action`, count(`id`) AS `count` FROM `logs` AS `Log` GROUP BY `action` ORDER BY count(`id`) DESC; Elapsed time: 1ms
操作类型统计查询成功: 12
Executed (default): SELECT `entityType`, count(`id`) AS `count` FROM `logs` AS `Log` GROUP BY `entityType` ORDER BY count(`id`) DESC; Elapsed time: 1ms
实体类型统计查询成功: 5
Executed (default): SELECT `username`, count(`id`) AS `loginCount`, MAX(`createdAt`) AS `lastLoginTime` FROM `logs` AS `Log` WHERE `Log`.`action` = 'user_login' AND `Log`.`createdAt` BETWEEN '2025-05-07 13:20:15' AND '2025-06-06 13:20:15' GROUP BY `username` ORDER BY count(`id`) DESC; Elapsed time: 1ms
用户登录统计查询成功: 4
Executed (default): SELECT `action`, count(`id`) AS `count` FROM `logs` AS `Log` WHERE `Log`.`entityType` = 'product' AND `Log`.`createdAt` BETWEEN '2025-05-07 13:20:15' AND '2025-06-06 13:20:15' GROUP BY `action` ORDER BY count(`id`) DESC; Elapsed time: 1ms
商品操作统计查询成功: 2
[0mGET /api/logs/stats [32m200[0m 14.156 ms - 1296[0m

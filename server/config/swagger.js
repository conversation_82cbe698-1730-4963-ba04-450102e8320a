/**
 * Swagger API文档配置
 * 自动生成API文档和接口测试界面
 */

const swaggerJSDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const path = require('path');

// Swagger定义
const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: '光年小卖部 API',
    version: '1.0.0',
    description: '光年小卖部B2C电商平台API文档',
    contact: {
      name: 'API支持',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: 'http://localhost:3000/api',
      description: '开发环境'
    },
    {
      url: 'http://**************:3000/api',
      description: '生产环境'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT认证token'
      }
    },
    schemas: {
      // 用户相关
      User: {
        type: 'object',
        properties: {
          id: { type: 'integer', description: '用户ID' },
          username: { type: 'string', description: '用户名' },
          email: { type: 'string', description: '邮箱' },
          role: { type: 'string', enum: ['user', 'admin'], description: '用户角色' },
          feishuUserId: { type: 'string', description: '飞书用户ID' },
          lyBalance: { type: 'number', description: '光年币余额' },
          isActive: { type: 'boolean', description: '是否激活' },
          createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
          updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
        }
      },
      
      // 商品相关
      Product: {
        type: 'object',
        properties: {
          id: { type: 'integer', description: '商品ID' },
          name: { type: 'string', description: '商品名称' },
          description: { type: 'string', description: '商品描述' },
          lyPrice: { type: 'number', description: '光年币价格' },
          rmbPrice: { type: 'number', description: '人民币价格' },
          stock: { type: 'integer', description: '库存数量' },
          categoryId: { type: 'integer', description: '分类ID' },
          status: { type: 'string', enum: ['active', 'inactive'], description: '商品状态' },
          isHot: { type: 'boolean', description: '是否热门' },
          isNew: { type: 'boolean', description: '是否新品' },
          exchangeCount: { type: 'integer', description: '兑换次数' },
          imageUrl: { type: 'string', description: '主图URL' },
          images: { type: 'array', items: { type: 'string' }, description: '图片列表' },
          createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
          updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
        }
      },
      
      // 分类相关
      Category: {
        type: 'object',
        properties: {
          id: { type: 'integer', description: '分类ID' },
          name: { type: 'string', description: '分类名称' },
          description: { type: 'string', description: '分类描述' },
          isActive: { type: 'boolean', description: '是否激活' },
          sortOrder: { type: 'integer', description: '排序顺序' },
          createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
          updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
        }
      },
      
      // 兑换订单相关
      Exchange: {
        type: 'object',
        properties: {
          id: { type: 'integer', description: '兑换ID' },
          orderNumber: { type: 'string', description: '订单号' },
          userId: { type: 'integer', description: '用户ID' },
          productId: { type: 'integer', description: '商品ID' },
          quantity: { type: 'integer', description: '兑换数量' },
          totalLyPrice: { type: 'number', description: '总光年币价格' },
          status: { type: 'string', enum: ['pending', 'approved', 'rejected', 'completed'], description: '兑换状态' },
          notes: { type: 'string', description: '备注' },
          createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
          updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
        }
      },
      
      // 反馈相关
      Feedback: {
        type: 'object',
        properties: {
          id: { type: 'integer', description: '反馈ID' },
          userId: { type: 'integer', description: '用户ID' },
          type: { type: 'string', enum: ['bug', 'feature', 'general'], description: '反馈类型' },
          title: { type: 'string', description: '反馈标题' },
          content: { type: 'string', description: '反馈内容' },
          status: { type: 'string', enum: ['open', 'in_progress', 'resolved', 'closed'], description: '处理状态' },
          priority: { type: 'string', enum: ['low', 'medium', 'high'], description: '优先级' },
          response: { type: 'string', description: '回复内容' },
          createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
          updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
        }
      },
      
      // 通用响应
      ApiResponse: {
        type: 'object',
        properties: {
          success: { type: 'boolean', description: '请求是否成功' },
          message: { type: 'string', description: '响应消息' },
          data: { type: 'object', description: '响应数据' },
          error: { type: 'string', description: '错误信息' }
        }
      },
      
      // 分页响应
      PaginatedResponse: {
        type: 'object',
        properties: {
          data: { type: 'array', description: '数据列表' },
          total: { type: 'integer', description: '总数量' },
          page: { type: 'integer', description: '当前页码' },
          limit: { type: 'integer', description: '每页数量' },
          totalPages: { type: 'integer', description: '总页数' }
        }
      },
      
      // 错误响应
      ErrorResponse: {
        type: 'object',
        properties: {
          message: { type: 'string', description: '错误消息' },
          status: { type: 'integer', description: '错误状态码' },
          error: { type: 'string', description: '错误详情' }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ]
};

// Swagger选项
const options = {
  definition: swaggerDefinition,
  apis: [
    path.join(__dirname, '../routes/*.js'),
    path.join(__dirname, '../controllers/*.js'),
    path.join(__dirname, '../models/*.js')
  ]
};

// 生成Swagger规范
const swaggerSpec = swaggerJSDoc(options);

// 自定义CSS样式
const customCss = `
  .swagger-ui .topbar { display: none; }
  .swagger-ui .info { margin: 20px 0; }
  .swagger-ui .info .title { color: #3b82f6; }
  .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; margin: 20px 0; border-radius: 8px; }
`;

// Swagger UI选项
const swaggerUiOptions = {
  customCss,
  customSiteTitle: '光年小卖部 API文档',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    docExpansion: 'none',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    tryItOutEnabled: true
  }
};

module.exports = {
  swaggerSpec,
  swaggerUi,
  swaggerUiOptions
};

#!/bin/bash

echo "=== 修复生产环境nginx配置（API+uploads+SPA路由） ==="

# 0. 本地检查配置文件语法
echo "0. 检查配置文件语法..."
if command -v nginx >/dev/null 2>&1; then
    nginx -t -c $(pwd)/complete-nginx-fix.conf >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "❌ 本地nginx配置语法检查失败，请检查complete-nginx-fix.conf文件"
        exit 1
    else
        echo "✅ 本地nginx配置语法检查通过"
    fi
else
    echo "⚠️ 本地未安装nginx，跳过语法检查"
fi

# 1. 上传完整配置文件
echo "1. 上传完整nginx配置文件..."
scp complete-nginx-fix.conf root@**************:/tmp/nginx-complete.conf

# 2. 在服务器上应用完整配置
echo "2. 在服务器上应用完整配置..."
ssh root@************** '
echo "📋 备份当前配置..."
cp /www/server/panel/vhost/nginx/**************.conf /www/server/panel/vhost/nginx/**************.conf.backup.$(date +%Y%m%d_%H%M%S)

echo "🔄 应用新的完整配置..."
cp /tmp/nginx-complete.conf /www/server/panel/vhost/nginx/**************.conf

echo "📁 创建并设置uploads目录..."
cd /www/wwwroot/server
mkdir -p uploads/images uploads/documents
chmod 755 uploads uploads/images uploads/documents
chown -R www:www uploads

echo "🔧 测试nginx配置..."
if nginx -t; then
    echo "✅ Nginx配置测试通过"
    
    echo "🔄 重新加载nginx..."
    nginx -s reload
    echo "✅ Nginx已重新加载"
    
    echo "🧪 测试功能..."
    
    # 测试API代理
    echo "📡 测试API代理："
    curl -I http://127.0.0.1/api/health 2>/dev/null | head -1 || echo "API测试可能需要后端服务运行"
    
    # 测试静态文件
    echo "📁 测试uploads静态文件："
    echo "test image" > uploads/test.png
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1/uploads/test.png)
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ uploads静态文件访问正常 (HTTP $HTTP_STATUS)"
    else
        echo "❌ uploads静态文件访问异常 (HTTP $HTTP_STATUS)"
    fi
    rm -f uploads/test.png
    
    # 测试前端路由
    echo "🌐 测试前端路由："
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1/)
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ 前端页面访问正常 (HTTP $HTTP_STATUS)"
    else
        echo "❌ 前端页面访问异常 (HTTP $HTTP_STATUS)"
    fi
    
else
    echo "❌ Nginx配置测试失败！"
    echo "显示配置文件内容："
    cat /www/server/panel/vhost/nginx/**************.conf
    exit 1
fi
'

echo ""
echo "3. 检查PM2后端服务状态..."
ssh root@************** "pm2 list | grep -E '(id|workyy)'"

echo ""
echo "=== 修复完成 ==="
echo "✅ 已添加以下关键配置："
echo "  📡 API代理：/api/* -> http://127.0.0.1:3000"
echo "  📁 静态文件：/uploads/* -> /www/wwwroot/server/uploads/"
echo "  🌐 SPA路由：/* -> /index.html"
echo ""
echo "🔗 现在应该可以正常使用："
echo "  • 飞书登录：http://**************/api/feishu/callback"
echo "  • 图片显示：http://**************/uploads/images/"
echo "  • 前端页面：http://**************/"
echo ""
echo "📝 请重新测试飞书登录和图片上传功能" 
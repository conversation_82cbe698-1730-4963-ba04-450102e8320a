#!/bin/bash

echo "=== 检查生产环境飞书配置 ==="

echo "1. 检查生产环境配置文件..."
echo "本地 .env.production 文件："
grep -E "(NODE_ENV|FEISHU_REDIRECT_URI)" server/.env.production

echo ""
echo "2. 检查服务器实际使用的配置..."
ssh root@************** "cd /www/wwwroot/server && echo '服务器 .env 文件：' && grep -E '(NODE_ENV|FEISHU_REDIRECT_URI)' .env"

echo ""
echo "3. 检查PM2应用状态..."
ssh root@************** "pm2 list | grep workyy"

echo ""
echo "4. 检查最近的飞书配置日志..."
ssh root@************** "pm2 logs workyy --lines 10 | grep -E '(飞书配置|FEISHU_REDIRECT_URI|NODE_ENV)'"

echo ""
echo "=== 需要在飞书开放平台配置的回调地址 ==="
echo "开发环境: http://localhost:3000/api/feishu/callback"
echo "生产环境: http://**************/api/feishu/callback"
echo ""
echo "请确保两个地址都已在飞书开放平台应用配置中添加！" 
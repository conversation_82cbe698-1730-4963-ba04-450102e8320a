#!/bin/bash

# 光年小卖部 - 构建脚本
# 用于构建前端和准备部署文件

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/dist"
SERVER_DIR="$PROJECT_ROOT/server"

echo -e "${BLUE}===== 光年小卖部构建脚本 =====${NC}"
echo -e "项目根目录: $PROJECT_ROOT"
echo -e "构建目录: $BUILD_DIR"
echo ""

# 检查Node.js和npm
check_dependencies() {
    echo -e "${YELLOW}检查依赖...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Node.js: $(node --version)${NC}"
    echo -e "${GREEN}✅ npm: $(npm --version)${NC}"
    echo ""
}

# 安装前端依赖
install_frontend_deps() {
    echo -e "${YELLOW}安装前端依赖...${NC}"
    cd "$PROJECT_ROOT"
    
    if [ ! -d "node_modules" ]; then
        echo "首次安装依赖..."
        npm install
    else
        echo "更新依赖..."
        npm ci
    fi
    
    echo -e "${GREEN}✅ 前端依赖安装完成${NC}"
    echo ""
}

# 安装后端依赖
install_backend_deps() {
    echo -e "${YELLOW}安装后端依赖...${NC}"
    cd "$SERVER_DIR"
    
    if [ ! -d "node_modules" ]; then
        echo "首次安装依赖..."
        npm install --production
    else
        echo "更新依赖..."
        npm ci --production
    fi
    
    echo -e "${GREEN}✅ 后端依赖安装完成${NC}"
    echo ""
}

# 构建前端
build_frontend() {
    echo -e "${YELLOW}构建前端...${NC}"
    cd "$PROJECT_ROOT"
    
    # 清理旧的构建文件
    if [ -d "$BUILD_DIR" ]; then
        echo "清理旧的构建文件..."
        rm -rf "$BUILD_DIR"
    fi
    
    # 执行构建
    npm run build
    
    if [ ! -d "$BUILD_DIR" ]; then
        echo -e "${RED}❌ 前端构建失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 前端构建完成${NC}"
    echo ""
}

# 复制必要文件到构建目录
copy_assets() {
    echo -e "${YELLOW}复制资源文件...${NC}"
    
    # 创建必要目录
    mkdir -p "$BUILD_DIR/uploads"
    mkdir -p "$BUILD_DIR/uploads/images"
    
    # 复制上传文件（如果存在）
    if [ -d "$SERVER_DIR/uploads" ]; then
        cp -r "$SERVER_DIR/uploads"/* "$BUILD_DIR/uploads/" 2>/dev/null || true
        echo "已复制上传文件"
    fi
    
    # 复制配置文件模板
    if [ -f "$PROJECT_ROOT/config/nginx/production.conf" ]; then
        cp "$PROJECT_ROOT/config/nginx/production.conf" "$BUILD_DIR/"
        echo "已复制nginx配置"
    fi
    
    echo -e "${GREEN}✅ 资源文件复制完成${NC}"
    echo ""
}

# 生成构建信息
generate_build_info() {
    echo -e "${YELLOW}生成构建信息...${NC}"
    
    BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    BUILD_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    BUILD_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
    
    cat > "$BUILD_DIR/build-info.json" << EOF
{
  "buildTime": "$BUILD_TIME",
  "gitHash": "$BUILD_HASH",
  "gitBranch": "$BUILD_BRANCH",
  "nodeVersion": "$(node --version)",
  "npmVersion": "$(npm --version)"
}
EOF
    
    echo -e "${GREEN}✅ 构建信息已生成${NC}"
    echo ""
}

# 验证构建结果
validate_build() {
    echo -e "${YELLOW}验证构建结果...${NC}"
    
    # 检查关键文件
    REQUIRED_FILES=(
        "$BUILD_DIR/index.html"
        "$BUILD_DIR/assets"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -e "$file" ]; then
            echo -e "${RED}❌ 缺少文件: $file${NC}"
            exit 1
        fi
    done
    
    # 计算构建大小
    BUILD_SIZE=$(du -sh "$BUILD_DIR" | cut -f1)
    
    echo -e "${GREEN}✅ 构建验证通过${NC}"
    echo -e "构建大小: $BUILD_SIZE"
    echo ""
}

# 显示构建摘要
show_summary() {
    echo -e "${BLUE}===== 构建完成 =====${NC}"
    echo -e "构建目录: $BUILD_DIR"
    echo -e "构建时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo -e "${GREEN}🎉 构建成功！${NC}"
    echo ""
    echo -e "${YELLOW}下一步操作:${NC}"
    echo -e "1. 部署到服务器: ./scripts/deploy/production-deploy.sh"
    echo -e "2. 本地预览: npm run preview"
    echo ""
}

# 主函数
main() {
    check_dependencies
    install_frontend_deps
    install_backend_deps
    build_frontend
    copy_assets
    generate_build_info
    validate_build
    show_summary
}

# 执行主函数
main "$@"
